import os
import random
from datetime import datetime
from typing import Optional, <PERSON><PERSON>
from fastapi import UploadFile
from obs import ObsClient
from config.env import ObsConfig
from utils.log_util import logger
from exceptions.exception import ServiceException


class ObsUtil:
    """
    华为云OBS工具类
    """

    @classmethod
    def get_obs_client(cls) -> ObsClient:
        """
        获取OBS客户端

        :return: OBS客户端实例
        """
        try:
            # 记录关键配置信息（隐藏敏感信息）
            logger.info(f"创建OBS客户端 - Bucket: {ObsConfig.obs_bucket}, Endpoint: {ObsConfig.obs_endpoint}")
            logger.info(f"Access Key: {ObsConfig.obs_access_key[:8]}***{ObsConfig.obs_access_key[-4:] if ObsConfig.obs_access_key else 'None'}")
            logger.info(f"Secret Key: {'已配置' if ObsConfig.obs_secret_key else '未配置'}")

            # 构建完整的endpoint URL
            endpoint = ObsConfig.obs_endpoint
            if not endpoint.startswith('https://'):
                endpoint = f'https://{endpoint}'

            obs_client = ObsClient(
                access_key_id=ObsConfig.obs_access_key,
                secret_access_key=ObsConfig.obs_secret_key,
                server=endpoint
            )
            logger.info("OBS客户端创建成功")
            return obs_client
        except Exception as e:
            logger.error(f"创建OBS客户端失败: {str(e)}")
            raise ServiceException(message="OBS服务连接失败")

    @classmethod
    def generate_random_number(cls) -> str:
        """
        生成3位数字构成的字符串

        :return: 3位数字构成的字符串
        """
        random_number = random.randint(1, 999)
        return f'{random_number:03}'

    @classmethod
    def generate_object_key(cls, file: UploadFile) -> str:
        """
        生成OBS对象键（文件路径）

        :param file: 上传文件对象
        :return: 对象键
        """
        # 获取文件扩展名
        file_extension = file.filename.rsplit('.', 1)[-1] if '.' in file.filename else ''
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 生成随机数
        random_num = cls.generate_random_number()
        
        # 生成文件名（不包含原始文件名，避免中文问题）
        filename = f"upload_{timestamp}_{random_num}.{file_extension}"
        
        # 生成对象键（按年/月/日分目录）
        date_path = datetime.now().strftime("%Y/%m/%d")
        object_key = f"{ObsConfig.obs_directory}/upload/{date_path}/{filename}"

        return object_key

    @classmethod
    async def upload_file_to_obs(cls, file: UploadFile) -> Tuple[str, str]:
        """
        上传文件到OBS

        :param file: 上传文件对象
        :return: (对象键, 完整URL)
        """
        obs_client = None
        try:
            logger.info(f"开始上传文件: {file.filename} ({file.content_type})")

            # 获取OBS客户端
            obs_client = cls.get_obs_client()

            # 生成对象键
            object_key = cls.generate_object_key(file)
            logger.info(f"生成对象键: {object_key}")

            # 读取文件内容
            file_content = await file.read()
            logger.info(f"文件大小: {len(file_content)} bytes")

            # 上传文件到OBS
            resp = obs_client.putObject(
                bucketName=ObsConfig.obs_bucket,
                objectKey=object_key,
                content=file_content,
                metadata={'Content-Type': file.content_type or 'application/octet-stream'}
            )

            logger.info(f"OBS响应状态: {resp.status}")

            if hasattr(resp, 'errorCode') and resp.errorCode:
                logger.error(f"错误代码: {resp.errorCode}")
            if hasattr(resp, 'errorMessage') and resp.errorMessage:
                logger.error(f"错误信息: {resp.errorMessage}")

            # 检查上传结果
            if resp.status < 300:
                # 生成完整的访问URL
                file_url = f"{ObsConfig.obs_url_prefix}/{object_key}"
                logger.info(f"文件上传成功: {object_key}")
                return object_key, file_url
            else:
                logger.error(f"文件上传失败: {getattr(resp, 'errorCode', 'Unknown')} - {getattr(resp, 'errorMessage', 'Unknown error')}")
                raise ServiceException(message=f"文件上传失败: {getattr(resp, 'errorMessage', 'Unknown error')}")

        except Exception as e:
            logger.error(f"上传文件到OBS失败: {str(e)}")
            if isinstance(e, ServiceException):
                raise e
            else:
                raise ServiceException(message="文件上传失败，请重试")
        finally:
            # 关闭OBS客户端
            if obs_client:
                obs_client.close()

    @classmethod
    def delete_file_from_obs(cls, object_key: str) -> bool:
        """
        从OBS删除文件

        :param object_key: 对象键
        :return: 删除是否成功
        """
        obs_client = None
        try:
            # 获取OBS客户端
            obs_client = cls.get_obs_client()
            
            # 删除文件
            resp = obs_client.deleteObject(
                bucketName=ObsConfig.obs_bucket,
                objectKey=object_key
            )
            
            # 检查删除结果
            if resp.status < 300:
                logger.info(f"文件删除成功: {object_key}")
                return True
            else:
                logger.error(f"文件删除失败: {resp.errorCode} - {resp.errorMessage}")
                return False
                
        except Exception as e:
            logger.error(f"从OBS删除文件失败: {str(e)}")
            return False
        finally:
            # 关闭OBS客户端
            if obs_client:
                obs_client.close()

    @classmethod
    def check_file_exists_in_obs(cls, object_key: str) -> bool:
        """
        检查文件在OBS中是否存在

        :param object_key: 对象键
        :return: 文件是否存在
        """
        obs_client = None
        try:
            # 获取OBS客户端
            obs_client = cls.get_obs_client()
            
            # 检查文件是否存在
            resp = obs_client.getObjectMetadata(
                bucketName=ObsConfig.obs_bucket,
                objectKey=object_key
            )
            
            return resp.status < 300
            
        except Exception as e:
            logger.error(f"检查OBS文件是否存在失败: {str(e)}")
            return False
        finally:
            # 关闭OBS客户端
            if obs_client:
                obs_client.close()

    @classmethod
    def extract_object_key_from_url(cls, file_url: str) -> Optional[str]:
        """
        从完整URL中提取对象键

        :param file_url: 完整的文件URL
        :return: 对象键，如果URL格式不正确则返回None
        """
        try:
            if file_url.startswith(ObsConfig.obs_url_prefix):
                # 移除域名部分，获取对象键
                object_key = file_url.replace(f"{ObsConfig.obs_url_prefix}/", "")
                return object_key
            return None
        except Exception as e:
            logger.error(f"提取对象键失败: {str(e)}")
            return None
