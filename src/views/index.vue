<template>
  <div class="app-container home">
    <!-- 数据卡片 -->
    <el-row :gutter="20" class="card-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon blue">
            <i class="el-icon-shopping-cart-full"></i>
          </div>
          <div class="card-content">
            <div class="card-title">总订单量</div>
            <div class="card-value">5,382</div>
            <div class="card-footer">
              <span class="up"><i class="el-icon-top"></i> 12.5%</span>
              较上周
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon green">
            <i class="el-icon-money"></i>
          </div>
          <div class="card-content">
            <div class="card-title">今日销售额</div>
            <div class="card-value">¥25,648</div>
            <div class="card-footer">
              <span class="up"><i class="el-icon-top"></i> 8.2%</span>
              较昨日
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon orange">
            <i class="el-icon-user"></i>
          </div>
          <div class="card-content">
            <div class="card-title">注册用户</div>
            <div class="card-value">2,856</div>
            <div class="card-footer">
              <span class="up"><i class="el-icon-top"></i> 5.3%</span>
              较上月
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="hover" class="data-card">
          <div class="card-icon purple">
            <i class="el-icon-s-order"></i>
          </div>
          <div class="card-content">
            <div class="card-title">待处理订单</div>
            <div class="card-value">128</div>
            <div class="card-footer">
              <span class="down"><i class="el-icon-bottom"></i> 3.1%</span>
              较昨日
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表部分 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>订单趋势（近7天）</span>
          </div>
          <div ref="orderChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>销售额趋势（近7天）</span>
          </div>
          <div ref="salesChart" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :md="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>服务类型分布</span>
          </div>
          <div ref="serviceChart" class="chart chart-small"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card shadow="hover" class="chart-card">
          <div slot="header" class="clearfix">
            <span>用户地域分布</span>
          </div>
          <div ref="userRegionChart" class="chart chart-small"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="table-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <div slot="header" class="clearfix">
            <span>最近订单</span>
            <el-button style="float: right; padding: 3px 0" type="text">查看更多</el-button>
          </div>
          <el-table :data="recentOrders" stripe style="width: 100%">
            <el-table-column prop="id" label="订单号" width="180"></el-table-column>
            <el-table-column prop="customer" label="客户"></el-table-column>
            <el-table-column prop="service" label="服务类型"></el-table-column>
            <el-table-column prop="amount" label="金额"></el-table-column>
            <el-table-column prop="time" label="下单时间"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === '已完成' ? 'success' : scope.row.status === '待处理' ? 'warning' : 'info'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "1.6.2",
      // 最近订单数据
      recentOrders: [
        { id: 'DD20230511001', customer: '张先生', service: '家居保洁', amount: '¥258', time: '2023-05-11 10:21', status: '已完成' },
        { id: 'DD20230511002', customer: '李女士', service: '厨房深度清洁', amount: '¥358', time: '2023-05-11 12:35', status: '已完成' },
        { id: 'DD20230510003', customer: '王先生', service: '家电清洗', amount: '¥199', time: '2023-05-10 16:42', status: '已完成' },
        { id: 'DD20230510002', customer: '赵女士', service: '全屋除螨', amount: '¥499', time: '2023-05-10 14:28', status: '进行中' },
        { id: 'DD20230510001', customer: '刘先生', service: '新居开荒', amount: '¥698', time: '2023-05-10 09:15', status: '待处理' }
      ],
      // 图表实例
      charts: {
        orderChart: null,
        salesChart: null,
        serviceChart: null,
        userRegionChart: null
      }
    };
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        // 订单趋势图
        this.charts.orderChart = echarts.init(this.$refs.orderChart);
        this.charts.orderChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: ['5-5', '5-6', '5-7', '5-8', '5-9', '5-10', '5-11']
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            data: [120, 132, 101, 134, 90, 158, 142],
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#1890ff'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(24,144,255,0.4)'
                }, {
                  offset: 1, color: 'rgba(24,144,255,0.1)'
                }]
              }
            }
          }]
        });

        // 销售额趋势图
        this.charts.salesChart = echarts.init(this.$refs.salesChart);
        this.charts.salesChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: ['5-5', '5-6', '5-7', '5-8', '5-9', '5-10', '5-11']
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}元'
            }
          },
          series: [{
            data: [19800, 22500, 18200, 25300, 20100, 24800, 25600],
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#52c41a'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(82,196,26,0.4)'
                }, {
                  offset: 1, color: 'rgba(82,196,26,0.1)'
                }]
              }
            }
          }]
        });

        // 服务类型分布图
        this.charts.serviceChart = echarts.init(this.$refs.serviceChart);
        this.charts.serviceChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: ['家居保洁', '厨房深度清洁', '家电清洗', '新居开荒', '全屋除螨', '其他服务']
          },
          series: [
            {
              name: '服务类型',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '12',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                {value: 1548, name: '家居保洁'},
                {value: 835, name: '厨房深度清洁'},
                {value: 734, name: '家电清洗'},
                {value: 635, name: '新居开荒'},
                {value: 490, name: '全屋除螨'},
                {value: 300, name: '其他服务'}
              ]
            }
          ]
        });

        // 用户地域分布图
        this.charts.userRegionChart = echarts.init(this.$refs.userRegionChart);
        this.charts.userRegionChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            data: ['广州', '深圳', '上海', '北京', '杭州', '成都', '武汉', '其他']
          },
          series: [
            {
              name: '用户数量',
              type: 'bar',
              data: [320, 302, 290, 274, 259, 220, 190, 450],
              itemStyle: {
                color: function(params) {
                  const colorList = [
                    '#5470c6', '#91cc75', '#fac858', '#ee6666',
                    '#73c0de', '#3ba272', '#fc8452', '#9a60b4'
                  ];
                  return colorList[params.dataIndex];
                }
              }
            }
          ]
        });

        // 监听窗口变化，调整图表大小
        window.addEventListener('resize', this.resizeCharts);
      });
    },
    // 调整图表大小
    resizeCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize();
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.home {
  padding-bottom: 20px;
  
  .welcome-header {
    text-align: center;
    margin-bottom: 20px;
    
    h2 {
      font-size: 24px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 10px;
    }
    
    p {
      font-size: 14px;
      color: #606266;
    }
  }
  
  .card-row {
    margin-bottom: 20px;
  }
  
  .data-card {
    height: 108px;
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-bottom: 20px;
    
    .card-icon {
      width: 80px;
      height: 80px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 32px;
        color: #fff;
      }
      
      &.blue {
        background: linear-gradient(to right, #1890ff, #36cbcb);
      }
      
      &.green {
        background: linear-gradient(to right, #52c41a, #b7eb8f);
      }
      
      &.orange {
        background: linear-gradient(to right, #fa8c16, #ffd666);
      }
      
      &.purple {
        background: linear-gradient(to right, #722ed1, #b37feb);
      }
    }
    
    .card-content {
      flex: 1;
      
      .card-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .card-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .card-footer {
        font-size: 12px;
        color: #909399;
        
        .up {
          color: #52c41a;
          margin-right: 5px;
        }
        
        .down {
          color: #f56c6c;
          margin-right: 5px;
        }
      }
    }
  }
  
  .chart-row {
    margin-bottom: 20px;
  }
  
  .chart-card {
    margin-bottom: 20px;
  }
  
  .chart {
    height: 300px;
    width: 100%;
  }
  
  .chart-small {
    height: 250px;
  }
  
  .table-row {
    margin-bottom: 20px;
  }
}
</style> 