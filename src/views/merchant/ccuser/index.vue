<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属门店" prop="storeUuid">
        <el-select
          v-model="queryParams.storeUuid"
          placeholder="请选择门店"
          clearable
          filterable
          @change="handleStoreChange"
          style="width: 200px;"
        >
          <el-option
            v-for="store in storeList"
            :key="store.storeUuid"
            :label="store.name"
            :value="store.storeUuid"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入客户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户来源" prop="source">
        <el-input
          v-model="queryParams.source"
          placeholder="请输入客户来源"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['merchant:ccuser:add']"
        >新增</el-button>
      </el-col>
      <!-- 修改功能暂时禁用 -->
      <!--
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['merchant:ccuser:edit']"
        >修改</el-button>
      </el-col>
      -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['merchant:ccuser:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ccuserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="客户编号" align="center" prop="id" width="100" />
      <el-table-column label="客户姓名" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="所属门店" align="center" width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ getStoreName(scope.row.storeUuid) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="mobile" width="130" />
      <el-table-column label="微信昵称" align="center" prop="wechatNickname" :show-overflow-tooltip="true" />
      <el-table-column label="性别" align="center" prop="sex" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.sex">{{ scope.row.sex === '男' ? '男' : scope.row.sex === '女' ? '女' : scope.row.sex }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="客户来源" align="center" prop="source" :show-overflow-tooltip="true" />
      <el-table-column label="会员等级" align="center" prop="level" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.level || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="金额" align="center" prop="amount" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.amount">￥{{ scope.row.amount }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="statusName" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
            {{ scope.row.statusName || '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime || scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template slot-scope="scope">
          <!-- 修改功能暂时禁用 -->
          <!--
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['merchant:ccuser:edit']"
          >修改</el-button>
          -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['merchant:ccuser:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改客户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入客户姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属门店" prop="storeUuid">
              <el-select
                v-model="form.storeUuid"
                placeholder="请选择门店"
                :disabled="!!currentStoreName"
                filterable
                style="width: 100%;"
              >
                <el-option
                  v-for="store in storeList"
                  :key="store.storeUuid"
                  :label="store.name"
                  :value="store.storeUuid"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="form.mobile" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户编号" prop="number">
              <el-input v-model="form.number" placeholder="请输入客户编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="微信号" prop="wechatNumber">
              <el-input v-model="form.wechatNumber" placeholder="请输入微信号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信昵称" prop="wechatNickname">
              <el-input v-model="form.wechatNickname" placeholder="请输入微信昵称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="sex">
              <el-select v-model="form.sex" placeholder="请选择性别" style="width: 100%;">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户来源" prop="source">
              <el-input v-model="form.source" placeholder="请输入客户来源" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="会员等级" prop="level">
              <el-input-number
                v-model="form.level"
                placeholder="请输入会员等级"
                :min="0"
                :max="999"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="1">正常</el-radio>
                <el-radio label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="身份证号" prop="idNumber">
          <el-input v-model="form.idNumber" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCcuser, getCcuser, delCcuser, addCcuser, updateCcuser } from "@/api/merchant/ccuser";
import { listStore } from "@/api/merchant/store";

export default {
  name: "Ccuser",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客户表格数据
      ccuserList: [],
      // 门店列表数据
      storeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前筛选的门店名称
      currentStoreName: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        mobile: null,
        status: null,
        source: null,
        storeUuid: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "客户姓名不能为空", trigger: "blur" }
        ],
        mobile: [
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getStoreList();
    this.initializeFromRoute();
  },
  watch: {
    // 监听路由变化，当路由查询参数改变时重新初始化
    '$route.query': {
      handler(newQuery, oldQuery) {
        // 只有当storeUuid发生变化时才重新初始化
        if (newQuery.storeUuid !== oldQuery.storeUuid) {
          this.initializeFromRoute();
        }
      },
      immediate: false
    }
  },
  methods: {
    /** 从路由初始化页面状态 */
    initializeFromRoute() {
      // 重置查询参数
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: null,
        mobile: null,
        status: null,
        source: null,
        storeUuid: null,
      };

      // 重置门店筛选状态
      this.currentStoreName = null;

      // 检查是否有从门店管理页面传递过来的查询参数
      if (this.$route.query.storeUuid) {
        this.queryParams.storeUuid = this.$route.query.storeUuid;
        this.currentStoreName = this.$route.query.storeName || `门店UUID: ${this.$route.query.storeUuid}`;
      }

      // 重新加载数据
      this.getList();
    },
    /** 获取门店列表 */
    getStoreList() {
      listStore({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.storeList = response.rows || [];
      }).catch(error => {
        console.error('获取门店列表失败:', error);
      });
    },
    /** 处理门店选择变化 */
    handleStoreChange(storeUuid) {
      if (storeUuid) {
        // 找到选中的门店信息
        const selectedStore = this.storeList.find(store => store.storeUuid === storeUuid);
        this.currentStoreName = selectedStore ? selectedStore.name : `门店UUID: ${storeUuid}`;

        // 更新路由查询参数（不跳转，只更新URL）
        this.$router.replace({
          path: '/merchant/ccuser',
          query: {
            storeUuid: storeUuid,
            storeName: this.currentStoreName
          }
        });
      } else {
        // 清除门店筛选
        this.currentStoreName = null;
        this.$router.replace({ path: '/merchant/ccuser' });
      }

      // 重新查询数据
      this.handleQuery();
    },
    /** 清除门店筛选 */
    clearStoreFilter() {
      // 清除路由查询参数并重新初始化
      this.$router.replace({ path: '/merchant/ccuser' }).then(() => {
        this.initializeFromRoute();
      });
    },
    /** 根据门店UUID获取门店名称 */
    getStoreName(storeUuid) {
      if (!storeUuid) {
        return '-';
      }
      const store = this.storeList.find(item => item.storeUuid === storeUuid);
      return store ? store.name : `门店UUID: ${storeUuid}`;
    },
    /** 查询客户列表 */
    getList() {
      this.loading = true;
      listCcuser(this.queryParams).then(response => {
        this.ccuserList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
        this.$modal.msgError('查询客户列表失败');
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        number: null,
        name: null,
        mobile: null,
        wechatNumber: null,
        wechatNickname: null,
        idNumber: null,
        sex: null,
        source: null,
        status: "1",
        level: 0,
        amount: null,
        remark: null,
        storeUuid: this.queryParams.storeUuid // 保持当前筛选的门店UUID
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 保存当前的门店筛选
      const currentStoreUuid = this.queryParams.storeUuid;
      const currentStoreName = this.currentStoreName;

      // 重置表单
      this.resetForm("queryForm");

      // 恢复门店筛选
      this.queryParams.storeUuid = currentStoreUuid;
      this.currentStoreName = currentStoreName;

      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加客户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids[0];
      getCcuser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改客户";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log('提交的表单数据:', this.form); // 调试日志
          if (this.form.id != null) {
            updateCcuser(this.form).then(response => {
              console.log('更新响应:', response); // 调试日志
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('更新失败:', error); // 调试日志
            });
          } else {
            addCcuser(this.form).then(response => {
              console.log('新增响应:', response); // 调试日志
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增失败:', error); // 调试日志
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除客户ID为"' + ids + '"的数据项？').then(function() {
        return delCcuser(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
