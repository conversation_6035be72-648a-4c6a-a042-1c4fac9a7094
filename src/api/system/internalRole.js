import request from '@/utils/request'

// 查询内部角色列表
export function listInternalRole(query) {
  return request({
    url: '/internal/role/list',
    method: 'get',
    params: query
  })
}

// 查询内部角色详细
export function getInternalRole(roleId) {
  return request({
    url: '/internal/role/' + roleId,
    method: 'get'
  })
}

// 新增内部角色
export function addInternalRole(data) {
  return request({
    url: '/internal/role/add',
    method: 'post',
    data: data
  })
}

// 修改内部角色
export function updateInternalRole(data) {
  return request({
    url: '/internal/role/edit',
    method: 'put',
    data: data
  })
}

// 内部角色状态修改
export function changeInternalRoleStatus(roleId, status) {
  return request({
    url: `/internal/role/${roleId}/status/${status}`,
    method: 'put'
  })
}

// 删除内部角色
export function delInternalRole(roleId) {
  return request({
    url: `/internal/role/${roleId}`,
    method: 'delete'
  })
}

// 查询内部角色已授权用户列表
export function allocatedUserList(query) {
  return request({
    url: '/internal/role/authUser/allocatedList',
    method: 'get',
    params: query
  })
}

// 查询内部角色未授权用户列表
export function unallocatedUserList(query) {
  return request({
    url: '/internal/role/authUser/unallocatedList',
    method: 'get',
    params: query
  })
}

// 取消用户授权内部角色
export function authUserCancel(data) {
  return request({
    url: '/internal/role/authUser/cancel',
    method: 'put',
    data: data
  })
}

// 批量取消用户授权内部角色
export function authUserCancelAll(data) {
  return request({
    url: '/internal/role/authUser/cancelAll',
    method: 'put',
    params: data
  })
}

// 授权用户选择
export function authUserSelectAll(data) {
  return request({
    url: '/internal/role/authUser/selectAll',
    method: 'put',
    params: data
  })
}

// 获取部门树列表
export function deptTreeSelect(roleId) {
  return request({
    url: '/internal/role/deptTree/' + roleId,
    method: 'get'
  })
} 