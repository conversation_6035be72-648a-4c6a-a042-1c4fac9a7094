import request from '@/utils/request'

// 查询软件收入流水列表
export function listSoftwareIncomeTransaction(query) {
  return request({
    url: '/finance/software-income/list',
    method: 'get',
    params: query
  })
}

// 查询软件收入流水详细
export function getSoftwareIncomeTransaction(transactionId) {
  return request({
    url: '/finance/transaction/' + transactionId,
    method: 'get'
  })
}

// 导出软件收入流水
export function exportSoftwareIncomeTransaction(query) {
  return request({
    url: '/finance/software-income/export',
    method: 'get',
    params: query
  })
}

// 获取软件收入统计数据
export function getSoftwareIncomeStatistics() {
  return request({
    url: '/finance/software-income/statistics',
    method: 'get'
  })
}
