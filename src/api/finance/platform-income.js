import request from '@/utils/request'

// 查询平台收入流水列表
export function listPlatformIncomeTransaction(query) {
  return request({
    url: '/finance/platform-income/list',
    method: 'get',
    params: query
  })
}

// 查询平台收入流水详细
export function getPlatformIncomeTransaction(transactionId) {
  return request({
    url: '/finance/transaction/' + transactionId,
    method: 'get'
  })
}

// 导出平台收入流水
export function exportPlatformIncomeTransaction(query) {
  return request({
    url: '/finance/platform-income/export',
    method: 'get',
    params: query
  })
}

// 获取平台收入统计数据
export function getPlatformIncomeStatistics() {
  return request({
    url: '/finance/platform-income/statistics',
    method: 'get'
  })
}
