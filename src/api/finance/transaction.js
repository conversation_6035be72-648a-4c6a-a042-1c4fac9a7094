import request from '@/utils/request'

// 查询公司资金流水列表
export function listTransaction(query) {
  return request({
    url: '/finance/transaction/list',
    method: 'get',
    params: query
  })
}

// 查询公司资金流水详细
export function getTransaction(transactionId) {
  return request({
    url: '/finance/transaction/' + transactionId,
    method: 'get'
  })
}

// 导出公司资金流水
export function exportTransaction(query) {
  return request({
    url: '/finance/transaction/export',
    method: 'get',
    params: query
  })
}
