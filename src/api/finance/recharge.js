import request from '@/utils/request'

// 查询充值管理列表
export function listRecharge(query) {
  return request({
    url: '/finance/recharge/list',
    method: 'get',
    params: query
  })
}

// 查询充值详细
export function getRecharge(transactionId) {
  return request({
    url: '/finance/recharge/' + transactionId,
    method: 'get'
  })
}

// 导出充值管理
export function exportRecharge(query) {
  return request({
    url: '/finance/recharge/export',
    method: 'get',
    params: query
  })
}
