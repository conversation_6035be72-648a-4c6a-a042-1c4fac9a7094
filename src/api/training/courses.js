import request from '@/utils/request'

// 查询培训课程列表
export function listTrainingCourses(query) {
  return request({
    url: '/training/courses/list',
    method: 'get',
    params: query
  })
}

// 查询培训课程详细
export function getTrainingCourses(courseId) {
  return request({
    url: '/training/courses/' + courseId,
    method: 'get'
  })
}

// 新增培训课程
export function addTrainingCourses(data) {
  return request({
    url: '/training/courses',
    method: 'post',
    data: data
  })
}

// 修改培训课程
export function updateTrainingCourses(data) {
  return request({
    url: '/training/courses',
    method: 'put',
    data: data
  })
}

// 删除培训课程
export function delTrainingCourses(courseIds) {
  return request({
    url: '/training/courses/' + courseIds,
    method: 'delete'
  })
}
