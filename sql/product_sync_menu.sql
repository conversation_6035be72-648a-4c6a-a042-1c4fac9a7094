-- 产品同步管理菜单 SQL
-- 注意：执行前请先确认父菜单ID，这里假设商户管理的菜单ID为 2000

-- 1. 首先查找商户管理父菜单ID（如果不存在则需要先创建）
-- SELECT menu_id FROM sys_menu WHERE menu_name = '商户管理' AND parent_id = 0;

-- 2. 如果商户管理菜单不存在，先创建商户管理一级菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('商户管理', 0, 4, 'merchant', NULL, 1, 0, 'M', '0', '0', '', 'merchant', 'admin', NOW(), '', NULL, '商户管理目录');

-- 3. 获取商户管理菜单ID（请根据实际情况调整）
SET @merchantMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '商户管理' AND parent_id = 0);

-- 4. 创建产品同步菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('产品同步', @merchantMenuId, 5, 'productSync', 'merchant/productSync/index', 1, 0, 'C', '0', '0', 'merchant:productSync:list', 'sync', 'admin', NOW(), '', NULL, '产品同步菜单');

-- 5. 获取产品同步菜单ID
SET @productSyncMenuId = (SELECT menu_id FROM sys_menu WHERE menu_name = '产品同步' AND parent_id = @merchantMenuId);

-- 6. 创建产品同步相关按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('产品同步查询', @productSyncMenuId, 1, '#', '', 1, 0, 'F', '0', '0', 'merchant:productSync:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('产品同步列表', @productSyncMenuId, 2, '#', '', 1, 0, 'F', '0', '0', 'merchant:productSync:list', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('产品数据同步', @productSyncMenuId, 3, '#', '', 1, 0, 'F', '0', '0', 'merchant:productSync:sync', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('产品同步导出', @productSyncMenuId, 4, '#', '', 1, 0, 'F', '0', '0', 'merchant:productSync:export', '#', 'admin', NOW(), '', NULL, '');

-- 7. 为超级管理员角色分配产品同步菜单权限（假设超级管理员角色ID为1）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, @productSyncMenuId);
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, (SELECT menu_id FROM sys_menu WHERE perms = 'merchant:productSync:query'));
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, (SELECT menu_id FROM sys_menu WHERE perms = 'merchant:productSync:list'));
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, (SELECT menu_id FROM sys_menu WHERE perms = 'merchant:productSync:sync'));
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, (SELECT menu_id FROM sys_menu WHERE perms = 'merchant:productSync:export'));

-- 查询结果验证
SELECT 
    m1.menu_name AS '一级菜单',
    m2.menu_name AS '二级菜单',
    m2.path AS '路由地址',
    m2.component AS '组件路径',
    m2.perms AS '权限标识',
    m2.icon AS '图标'
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.menu_id = m2.parent_id
WHERE m1.menu_name = '商户管理' AND m1.parent_id = 0
ORDER BY m2.order_num;
