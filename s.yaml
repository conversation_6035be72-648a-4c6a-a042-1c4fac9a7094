edition: 3.0.0
name: fc3-example
access: default
resources:
  fcDemo:
    component: fc3
    props:
      region: cn-shenzhen
      handler: index.handler
      role: ''
      disableOndemand: false
      description: ''
      timeout: 60
      diskSize: 512
      internetAccess: false
      layers:
        - acs:fc:cn-shenzhen:1028527784207010:layers/yunapi/versions/2
      customRuntimeConfig:
        port: 9099
        command:
          - python3
          - app.py
          - '--env=prod'
      logConfig:
        enableRequestMetrics: true
        enableInstanceMetrics: true
        logBeginRule: DefaultRegex
        project: serverless-cn-shenzhen-46357b1e-8ea0-586c-aeea-879ded05ac4b
        logstore: default-logs
      functionName: yunapi
      runtime: custom.debian10
      cpu: 0.35
      instanceConcurrency: 10
      memorySize: 512
      environmentVariables:
        PATH: >-
          /var/fc/lang/python3.10/bin:/usr/local/bin/apache-maven/bin:/usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/ruby/bin:/opt/bin:/code:/code/bin
        APP_ENV: prod
        DB_PORT: '3306'
        APP_NAME: JgAdmin-YunFastAPI
        APP_ROOT_PATH: /prod-api
        APP_VERSION: 1.6.2
        JWT_SECRET_KEY: b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55
        DB_POOL_TIMEOUT: '30'
        DB_MAX_OVERFLOW: '10'
        APP_SAME_TIME_LOGIN: 'true'
        DB_USERNAME: jingang
        REDIS_PORT: '6379'
        LD_LIBRARY_PATH: >-
          /code:/code/lib:/usr/local/lib:/opt/lib:/opt/php8.1/lib:/opt/php8.0/lib:/opt/php7.2/lib
        APP_HOST: 0.0.0.0
        REDIS_DATABASE: '8'
        REDIS_PASSWORD: Xyjrds#1811517%!$
        PYTHONPATH: /opt/python:/code
        REDIS_HOST: r-wz9b82d6941522a4pd.redis.rds.aliyuncs.com
        APP_RELOAD: 'false'
        DB_ECHO: 'true'
        JWT_ALGORITHM: HS256
        DB_POOL_SIZE: '50'
        DB_HOST: rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com
        JWT_REDIS_EXPIRE_MINUTES: '30'
        JWT_EXPIRE_MINUTES: '1440'
        DB_TYPE: mysql
        APP_IP_LOCATION_QUERY: 'true'
        DB_POOL_RECYCLE: '3600'
        REDIS_USERNAME: ''
        APP_PORT: '9099'
        DB_DATABASE: jingangdata
        DB_PASSWORD: jg$5170701
      vpcConfig:
        securityGroupId: sg-wz918nc11b3qtxzsqsul
        vpcId: vpc-wz9g5vb7ckkr4q0i5yc3b
        vSwitchIds:
          - vsw-wz90xj6n7skumwhzi394z
      code: ./
      triggers:
        - triggerConfig:
            methods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            authType: anonymous
            disableURLInternet: false
          triggerName: defaultTrigger
          description: ''
          qualifier: LATEST
          triggerType: http
