from sqlalchemy import and_, select, func, text
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.product_do import Product
from module_admin.entity.do.company_do import Company
from module_admin.entity.vo.product_sync_vo import ProductSyncPageQueryModel, ProductSyncExportModel
from utils.page_util import PageUtil


class ProductSyncDao:
    """
    产品同步管理模块数据库操作层
    """

    @classmethod
    async def get_product_sync_list(cls, query_db: AsyncSession, query_object: ProductSyncPageQueryModel, is_page: bool = False):
        """
        获取产品同步列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 产品同步列表信息对象
        """
        # 构建基础查询，使用LEFT JOIN关联product和company表
        query = select(
            Product.id,
            Product.product_name,
            Product.company_uuid,
            Company.id.label('company_id'),
            Company.name.label('company_name'),
            Product.service_skill_name,
            Product.service_skill_id,
            Product.img_id,
            Product.serve_type_name,
            Product.online_store_num,
            Product.sum_num,
            Product.type_name,
            Product.product_status,
            Product.is_delete,
            Product.create_time,
            Product.update_time
        ).select_from(
            Product.__table__.outerjoin(Company.__table__, Product.company_uuid == Company.id)
        ).where(
            Product.is_delete == 0
        )

        # 动态条件筛选
        if query_object.product_name:
            query = query.where(Product.product_name.like(f'%{query_object.product_name}%'))
        
        if query_object.company_name:
            query = query.where(Company.name.like(f'%{query_object.company_name}%'))
        
        if query_object.service_skill_name:
            query = query.where(Product.service_skill_name.like(f'%{query_object.service_skill_name}%'))
        
        if query_object.product_status is not None:
            query = query.where(Product.product_status == query_object.product_status)

        # 时间范围筛选
        if query_object.begin_time:
            query = query.where(Product.create_time >= query_object.begin_time)
        
        if query_object.end_time:
            query = query.where(Product.create_time <= query_object.end_time)

        # 排序：按创建时间倒序
        query = query.order_by(Product.create_time.desc())

        if is_page:
            # 分页查询
            return await PageUtil.paginate(query_db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await query_db.execute(query)
            return result.fetchall()

    @classmethod
    async def get_product_sync_detail_by_id(cls, query_db: AsyncSession, product_id: int):
        """
        根据产品ID获取产品同步详细信息

        :param query_db: orm对象
        :param product_id: 产品ID
        :return: 产品同步详细信息对象
        """
        query = select(
            Product.id,
            Product.product_name,
            Product.company_uuid,
            Company.id.label('company_id'),
            Company.name.label('company_name'),
            Product.service_skill_name,
            Product.service_skill_id,
            Product.img_id,
            Product.serve_type_name,
            Product.online_store_num,
            Product.sum_num,
            Product.type_name,
            Product.product_status,
            Product.is_delete,
            Product.details,
            Product.min_number,
            Product.max_number,
            Product.video_id,
            Product.create_time,
            Product.update_time
        ).select_from(
            Product.__table__.outerjoin(Company.__table__, Product.company_uuid == Company.id)
        ).where(
            and_(Product.id == product_id, Product.is_delete == 0)
        )

        result = await query_db.execute(query)
        return result.first()

    @classmethod
    async def get_product_sync_export_list(cls, query_db: AsyncSession, query_object: ProductSyncExportModel):
        """
        获取产品同步导出列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 产品同步导出列表信息对象
        """
        # 构建导出查询
        query = select(
            Product.id,
            Product.product_name,
            Product.company_uuid,
            Company.id.label('company_id'),
            Company.name.label('company_name'),
            Product.service_skill_name,
            Product.service_skill_id,
            Product.online_store_num,
            Product.sum_num,
            Product.type_name,
            Product.product_status,
            Product.create_time,
            Product.update_time
        ).select_from(
            Product.__table__.outerjoin(Company.__table__, Product.company_uuid == Company.id)
        ).where(
            Product.is_delete == 0
        )

        # 动态条件筛选
        if query_object.product_name:
            query = query.where(Product.product_name.like(f'%{query_object.product_name}%'))
        
        if query_object.company_name:
            query = query.where(Company.name.like(f'%{query_object.company_name}%'))
        
        if query_object.service_skill_name:
            query = query.where(Product.service_skill_name.like(f'%{query_object.service_skill_name}%'))
        
        if query_object.product_status is not None:
            query = query.where(Product.product_status == query_object.product_status)

        # 时间范围筛选
        if query_object.begin_time:
            query = query.where(Product.create_time >= query_object.begin_time)
        
        if query_object.end_time:
            query = query.where(Product.create_time <= query_object.end_time)

        # 排序：按创建时间倒序
        query = query.order_by(Product.create_time.desc())

        result = await query_db.execute(query)
        return result.fetchall()

    @classmethod
    async def get_product_sync_statistics(cls, query_db: AsyncSession):
        """
        获取产品同步统计信息

        :param query_db: orm对象
        :return: 统计信息对象
        """
        # 统计总产品数
        total_products_query = select(func.count(Product.id)).where(Product.is_delete == 0)
        total_products_result = await query_db.execute(total_products_query)
        total_products = total_products_result.scalar()

        # 统计有公司关联的产品数
        linked_products_query = select(func.count(Product.id)).where(
            and_(Product.is_delete == 0, Product.company_uuid.isnot(None))
        )
        linked_products_result = await query_db.execute(linked_products_query)
        linked_products = linked_products_result.scalar()

        # 统计无公司关联的产品数
        unlinked_products = total_products - linked_products

        # 统计不同状态的产品数
        status_query = select(
            Product.product_status,
            func.count(Product.id).label('count')
        ).where(
            Product.is_delete == 0
        ).group_by(Product.product_status)
        
        status_result = await query_db.execute(status_query)
        status_stats = {row.product_status: row.count for row in status_result.fetchall()}

        return {
            'total_products': total_products,
            'linked_products': linked_products,
            'unlinked_products': unlinked_products,
            'status_stats': status_stats
        }
