from sqlalchemy import and_, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from module_admin.entity.do.company_do import Company
from module_admin.entity.do.store_do import Store
from module_admin.entity.do.store_info_do import StoreInfo
from module_admin.entity.do.software_version_do import SoftwareVersion
from module_admin.entity.do.company_version_relation_do import CompanyVersionRelation
from module_admin.entity.do.batch_operation_log_do import Batch<PERSON>perationLog
from module_admin.entity.do.client_menu_do import ClientMenu
from module_admin.entity.do.version_menu_relation_do import VersionMenuRelation
from module_admin.entity.do.internal_user_do import InternalUser
from module_admin.entity.do.internal_user_permission_do import InternalUserPermission
from module_admin.entity.do.store_info_do import StoreInfo
from module_admin.entity.do.pay_type_do import PayType
from module_admin.entity.do.product_do import Product
from module_admin.entity.do.product_sku_do import ProductSku
from module_admin.entity.vo.company_vo import CompanyPageQueryModel, DeleteCompanyModel
from utils.page_util import PageUtil


class CompanyDao:
    """
    公司管理模块数据库操作层
    """

    @classmethod
    async def add_company_dao(cls, db: AsyncSession, company_data: dict):
        """
        新增公司数据库操作

        :param db: orm对象
        :param company_data: 公司数据字典
        :return: 新增的公司对象
        """
        db_company = Company(**company_data)
        db.add(db_company)
        await db.flush()

        return db_company

    @classmethod
    async def add_store_dao(cls, db: AsyncSession, store_data: dict):
        """
        新增门店数据库操作

        :param db: orm对象
        :param store_data: 门店数据字典
        :return: 新增的门店对象
        """
        db_store = Store(**store_data)
        db.add(db_store)
        await db.flush()

        return db_store

    @classmethod
    async def add_store_info_dao(cls, db: AsyncSession, store_info_data: dict):
        """
        新增门店详细信息数据库操作

        :param db: orm对象
        :param store_info_data: 门店详细信息数据字典
        :return: 新增的门店详细信息对象
        """
        db_store_info = StoreInfo(**store_info_data)
        db.add(db_store_info)
        await db.flush()

        return db_store_info

    @classmethod
    async def add_company_version_relation_dao(cls, db: AsyncSession, relation_data: dict):
        """
        新增公司版本关联数据库操作

        :param db: orm对象
        :param relation_data: 公司版本关联数据字典
        :return: 新增的公司版本关联对象
        """
        db_relation = CompanyVersionRelation(**relation_data)
        db.add(db_relation)
        await db.flush()

        return db_relation

    @classmethod
    async def get_software_version_by_uuid(cls, db: AsyncSession, uuid: str):
        """
        根据UUID获取软件版本信息

        :param db: orm对象
        :param uuid: 软件版本UUID
        :return: 软件版本信息对象
        """
        version_info = (await db.execute(select(SoftwareVersion).where(SoftwareVersion.uuid == uuid))).scalars().first()

        return version_info

    @classmethod
    async def add_internal_user_dao(cls, db: AsyncSession, user_data: dict):
        """
        新增内部用户数据库操作

        :param db: orm对象
        :param user_data: 内部用户数据字典
        :return: 新增的内部用户对象
        """
        # 使用ORM模型插入数据
        db_internal_user = InternalUser(**user_data)
        db.add(db_internal_user)
        await db.flush()

        return db_internal_user

    @classmethod
    async def add_internal_user_permission_dao(cls, db: AsyncSession, permission_data: dict):
        """
        新增内部用户权限数据库操作

        :param db: orm对象
        :param permission_data: 内部用户权限数据字典
        :return: 新增的内部用户权限对象
        """
        # 使用ORM模型插入数据
        db_internal_user_permission = InternalUserPermission(**permission_data)
        db.add(db_internal_user_permission)
        await db.flush()

        return db_internal_user_permission

    @classmethod
    async def get_company_list(cls, query_db: AsyncSession, query_object: CompanyPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取公司列表信息

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司列表信息对象
        """
        from sqlalchemy import func

        # 使用LEFT JOIN查询公司及其关联的版本信息
        query = select(
            Company,
            func.group_concat(
                func.concat(
                    SoftwareVersion.name,
                    ':',
                    func.date_format(CompanyVersionRelation.expire_time, '%Y-%m-%d %H:%i:%s')
                ).distinct()
            ).label('version_info')
        ).select_from(
            Company.__table__.outerjoin(CompanyVersionRelation.__table__, Company.id == CompanyVersionRelation.company_uuid)
            .outerjoin(SoftwareVersion.__table__, CompanyVersionRelation.version_uuid == SoftwareVersion.uuid)
        ).where(Company.is_delete == 0)

        # 构建查询条件
        if query_object.name:
            query = query.where(Company.name.like(f'%{query_object.name}%'))
        if query_object.city:
            query = query.where(Company.city.like(f'%{query_object.city}%'))
        if query_object.status:
            query = query.where(Company.status == query_object.status)
        if query_object.begin_time and query_object.end_time:
            query = query.where(
                and_(
                    Company.create_time >= query_object.begin_time,
                    Company.create_time <= query_object.end_time
                )
            )

        # 按公司分组
        query = query.group_by(Company.id)

        # 排序
        query = query.order_by(Company.create_time.desc())

        if is_page:
            # 分页查询 - 需要特殊处理因为使用了GROUP BY
            # 先获取总数
            count_query = select(func.count(func.distinct(Company.id))).select_from(
                Company.__table__.outerjoin(CompanyVersionRelation.__table__, Company.id == CompanyVersionRelation.company_uuid)
                .outerjoin(SoftwareVersion.__table__, CompanyVersionRelation.version_uuid == SoftwareVersion.uuid)
            ).where(Company.is_delete == 0)

            # 应用相同的过滤条件
            if query_object.name:
                count_query = count_query.where(Company.name.like(f'%{query_object.name}%'))
            if query_object.city:
                count_query = count_query.where(Company.city.like(f'%{query_object.city}%'))
            if query_object.status:
                count_query = count_query.where(Company.status == query_object.status)
            if query_object.begin_time and query_object.end_time:
                count_query = count_query.where(
                    and_(
                        Company.create_time >= query_object.begin_time,
                        Company.create_time <= query_object.end_time
                    )
                )

            total = (await query_db.execute(count_query)).scalar()

            # 执行分页查询
            result = await query_db.execute(query.offset((query_object.page_num - 1) * query_object.page_size).limit(query_object.page_size))

            # 处理结果
            companies_with_versions = []
            for row in result:
                company = row[0]
                version_info_str = row[1]

                # 解析版本信息
                version_names = []
                if version_info_str:
                    version_items = version_info_str.split(',')
                    for item in version_items:
                        if ':' in item:
                            name, expire_time = item.split(':', 1)
                            version_names.append(f"{name}:{expire_time}")
                        else:
                            version_names.append(item)

                # 将公司对象转换为字典并添加版本名称
                company_dict = {
                    'id': company.id,
                    'name': company.name,
                    'city_id': company.city_id,
                    'city': company.city,
                    'address': company.address,
                    'address_desc': company.address_desc,
                    'balance': company.balance,
                    'frozen_amount': company.frozen_amount,
                    'withdrawal_fee_rate': company.withdrawal_fee_rate,
                    'split_fee_rate': company.split_fee_rate,
                    'status': company.status,
                    'is_delete': company.is_delete,
                    'create_time': company.create_time,
                    'update_time': company.update_time,
                    'version_names': version_names
                }
                companies_with_versions.append(company_dict)

            import math
            has_next = math.ceil(total / query_object.page_size) > query_object.page_num

            from utils.page_util import PageResponseModel
            from utils.common_util import CamelCaseUtil

            return PageResponseModel(
                rows=CamelCaseUtil.transform_result(companies_with_versions),
                pageNum=query_object.page_num,
                pageSize=query_object.page_size,
                total=total,
                hasNext=has_next,
            )
        else:
            # 不分页查询
            result = await query_db.execute(query)
            companies_with_versions = []
            for row in result:
                company = row[0]
                version_info_str = row[1]

                # 解析版本信息
                version_names = []
                if version_info_str:
                    version_items = version_info_str.split(',')
                    for item in version_items:
                        if ':' in item:
                            name, expire_time = item.split(':', 1)
                            version_names.append(f"{name}:{expire_time}")
                        else:
                            version_names.append(item)

                # 将公司对象转换为字典并添加版本名称
                company_dict = {
                    'id': company.id,
                    'name': company.name,
                    'city_id': company.city_id,
                    'city': company.city,
                    'address': company.address,
                    'address_desc': company.address_desc,
                    'balance': company.balance,
                    'frozen_amount': company.frozen_amount,
                    'withdrawal_fee_rate': company.withdrawal_fee_rate,
                    'split_fee_rate': company.split_fee_rate,
                    'status': company.status,
                    'is_delete': company.is_delete,
                    'create_time': company.create_time,
                    'update_time': company.update_time,
                    'version_names': version_names
                }
                companies_with_versions.append(company_dict)

            from utils.common_util import CamelCaseUtil
            return CamelCaseUtil.transform_result(companies_with_versions)

    @classmethod
    async def get_company_detail_by_id(cls, query_db: AsyncSession, company_id: str):
        """
        根据公司id获取公司详细信息

        :param query_db: orm对象
        :param company_id: 公司id
        :return: 公司信息对象
        """
        from sqlalchemy import func

        # 使用LEFT JOIN查询公司及其关联的版本信息
        query = select(
            Company,
            func.group_concat(
                func.concat(
                    SoftwareVersion.name,
                    ':',
                    func.date_format(CompanyVersionRelation.expire_time, '%Y-%m-%d %H:%i:%s')
                ).distinct()
            ).label('version_info')
        ).select_from(
            Company.__table__.outerjoin(CompanyVersionRelation.__table__, Company.id == CompanyVersionRelation.company_uuid)
            .outerjoin(SoftwareVersion.__table__, CompanyVersionRelation.version_uuid == SoftwareVersion.uuid)
        ).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        ).group_by(Company.id)

        result = await query_db.execute(query)
        row = result.first()

        if row:
            company = row[0]
            version_info_str = row[1]

            # 解析版本信息
            version_names = []
            if version_info_str:
                version_items = version_info_str.split(',')
                for item in version_items:
                    if ':' in item:
                        name, expire_time = item.split(':', 1)
                        version_names.append(f"{name}:{expire_time}")
                    else:
                        version_names.append(item)

            # 将公司对象转换为字典并添加版本名称
            company_dict = {
                'id': company.id,
                'name': company.name,
                'city_id': company.city_id,
                'city': company.city,
                'address': company.address,
                'address_desc': company.address_desc,
                'balance': company.balance,
                'frozen_amount': company.frozen_amount,
                'withdrawal_fee_rate': company.withdrawal_fee_rate,
                'split_fee_rate': company.split_fee_rate,
                'status': company.status,
                'is_delete': company.is_delete,
                'create_time': company.create_time,
                'update_time': company.update_time,
                'version_names': version_names
            }

            from utils.common_util import CamelCaseUtil
            return CamelCaseUtil.transform_result([company_dict])[0]

        return None

    @classmethod
    async def edit_company_dao(cls, query_db: AsyncSession, company: Company):
        """
        编辑公司数据库操作

        :param query_db: orm对象
        :param company: 公司对象
        :return: 编辑校验结果
        """
        await query_db.merge(company)

    @classmethod
    async def delete_company_dao(cls, query_db: AsyncSession, delete_company: DeleteCompanyModel):
        """
        删除公司数据库操作

        :param query_db: orm对象
        :param delete_company: 删除公司对象
        :return: 删除校验结果
        """
        company_ids = delete_company.ids.split(',')
        query = update(Company).where(Company.id.in_(company_ids)).values(is_delete='1')
        await query_db.execute(query)

    @classmethod
    async def get_company_detail_by_name(cls, query_db: AsyncSession, company_name: str):
        """
        根据公司名称获取公司详细信息

        :param query_db: orm对象
        :param company_name: 公司名称
        :return: 公司信息对象
        """
        query = select(Company).where(
            and_(Company.name == company_name, Company.is_delete == 0)
        )
        result = await query_db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_company_stores_with_info(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司下的所有门店及其详细信息

        :param query_db: orm对象
        :param company_id: 公司id
        :return: 门店及详细信息列表
        """
        # 联查门店和门店详细信息
        query = select(Store, StoreInfo).outerjoin(
            StoreInfo, Store.id == StoreInfo.store_id
        ).where(
            and_(Store.company_id == company_id, Store.is_delete == 0)
        ).order_by(Store.create_time.desc())

        result = await query_db.execute(query)
        rows = result.all()

        # 组织数据结构
        stores_detail = []
        for store, store_info in rows:
            store_data = {
                'store': store,
                'store_info': store_info
            }
            stores_detail.append(store_data)

        return stores_detail

    @classmethod
    async def get_company_balance(cls, query_db: AsyncSession, company_id: str) -> Decimal:
        """
        获取公司余额

        :param query_db: orm对象
        :param company_id: 公司ID
        :return: 公司余额
        """
        query = select(Company.balance).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        )
        result = await query_db.execute(query)
        balance = result.scalar()
        return balance if balance is not None else Decimal('0.00')

    @classmethod
    async def update_company_balance(cls, query_db: AsyncSession, company_id: str, new_balance: Decimal):
        """
        更新公司余额

        :param query_db: orm对象
        :param company_id: 公司ID
        :param new_balance: 新余额
        :return: 更新结果
        """
        query = update(Company).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        ).values(balance=new_balance)
        await query_db.execute(query)

    @classmethod
    async def get_company_frozen_amount(cls, query_db: AsyncSession, company_id: str) -> Decimal:
        """
        获取公司冻结资金

        :param query_db: orm对象
        :param company_id: 公司ID
        :return: 冻结资金
        """
        query = select(Company.frozen_amount).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        )
        result = await query_db.execute(query)
        frozen_amount = result.scalar()
        return frozen_amount if frozen_amount is not None else Decimal('0.00')

    @classmethod
    async def update_company_frozen_amount(cls, query_db: AsyncSession, company_id: str, new_frozen_amount: Decimal):
        """
        更新公司冻结资金

        :param query_db: orm对象
        :param company_id: 公司ID
        :param new_frozen_amount: 新冻结资金
        :return: 更新结果
        """
        query = update(Company).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        ).values(frozen_amount=new_frozen_amount)
        await query_db.execute(query)

    @classmethod
    async def freeze_company_funds(cls, query_db: AsyncSession, company_id: str, freeze_amount: Decimal):
        """
        冻结公司资金（从余额转移到冻结资金）

        :param query_db: orm对象
        :param company_id: 公司ID
        :param freeze_amount: 冻结金额
        :return: 操作结果
        """
        # 获取当前余额和冻结资金
        current_balance = await cls.get_company_balance(query_db, company_id)
        current_frozen = await cls.get_company_frozen_amount(query_db, company_id)

        # 检查余额是否足够
        if current_balance < freeze_amount:
            raise ValueError(f'余额不足，当前余额：{current_balance}，冻结金额：{freeze_amount}')

        # 更新余额和冻结资金
        new_balance = current_balance - freeze_amount
        new_frozen = current_frozen + freeze_amount

        query = update(Company).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        ).values(balance=new_balance, frozen_amount=new_frozen)
        await query_db.execute(query)

        return {'new_balance': new_balance, 'new_frozen': new_frozen}

    @classmethod
    async def unfreeze_company_funds(cls, query_db: AsyncSession, company_id: str, unfreeze_amount: Decimal, return_to_balance: bool = True):
        """
        解冻公司资金

        :param query_db: orm对象
        :param company_id: 公司ID
        :param unfreeze_amount: 解冻金额
        :param return_to_balance: 是否返回到余额（True：返回余额，False：直接扣除）
        :return: 操作结果
        """
        # 获取当前余额和冻结资金
        current_balance = await cls.get_company_balance(query_db, company_id)
        current_frozen = await cls.get_company_frozen_amount(query_db, company_id)

        # 检查冻结资金是否足够
        if current_frozen < unfreeze_amount:
            raise ValueError(f'冻结资金不足，当前冻结资金：{current_frozen}，解冻金额：{unfreeze_amount}')

        # 更新余额和冻结资金
        new_frozen = current_frozen - unfreeze_amount
        new_balance = current_balance + unfreeze_amount if return_to_balance else current_balance

        query = update(Company).where(
            and_(Company.id == company_id, Company.is_delete == 0)
        ).values(balance=new_balance, frozen_amount=new_frozen)
        await query_db.execute(query)

        return {'new_balance': new_balance, 'new_frozen': new_frozen}

    @classmethod
    async def get_company_by_id(cls, query_db: AsyncSession, company_id: str):
        """
        根据公司ID获取公司信息

        :param query_db: orm对象
        :param company_id: 公司ID
        :return: 公司信息
        """
        try:
            from utils.log_util import logger
            logger.info(f"查询公司信息，公司ID: {company_id}")

            query = select(Company).where(
                and_(Company.id == company_id, Company.is_delete == 0)
            )
            result = await query_db.execute(query)
            company = result.scalars().first()

            if company:
                logger.info(f"成功找到公司: {company.name} (ID: {company.id})")
            else:
                logger.warning(f"未找到公司，ID: {company_id}")

            return company
        except Exception as e:
            from utils.log_util import logger
            logger.error(f"查询公司信息失败，公司ID: {company_id}, 错误: {str(e)}")
            raise

    @classmethod
    async def add_pay_type_dao(cls, db: AsyncSession, pay_type_data: dict):
        """
        新增支付类型数据库操作

        :param db: orm对象
        :param pay_type_data: 支付类型数据字典
        :return: 新增的支付类型对象
        """
        db_pay_type = PayType(**pay_type_data)
        db.add(db_pay_type)
        await db.flush()

        return db_pay_type

    @classmethod
    async def batch_add_pay_types_dao(cls, db: AsyncSession, pay_types_data: list):
        """
        批量新增支付类型数据库操作

        :param db: orm对象
        :param pay_types_data: 支付类型数据列表
        :return: 新增的支付类型对象列表
        """
        pay_type_objects = []
        for pay_type_data in pay_types_data:
            db_pay_type = PayType(**pay_type_data)
            db.add(db_pay_type)
            pay_type_objects.append(db_pay_type)

        await db.flush()
        return pay_type_objects

    @classmethod
    async def get_template_products_dao(cls, db: AsyncSession, start_id: int = 48, end_id: int = 68):
        """
        获取模板产品数据

        :param db: orm对象
        :param start_id: 开始ID
        :param end_id: 结束ID
        :return: 模板产品列表
        """
        query = select(Product).where(
            and_(Product.id >= start_id, Product.id <= end_id)
        )
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_pay_type_name_by_code(cls, db: AsyncSession, company_uuid: str, pay_type_code: str):
        """
        根据支付类型代码获取支付类型名称

        :param db: orm对象
        :param company_uuid: 公司UUID
        :param pay_type_code: 支付类型代码
        :return: 支付类型名称
        """
        query = select(PayType.pay_type_name).where(
            and_(
                PayType.company_uuid == company_uuid,
                PayType.pay_type_code == pay_type_code,
                PayType.is_enabled == 1
            )
        )
        result = await db.execute(query)
        pay_type_name = result.scalars().first()
        return pay_type_name

    @classmethod
    async def get_pay_types_mapping(cls, db: AsyncSession, company_uuid: str):
        """
        获取公司的支付类型映射关系

        :param db: orm对象
        :param company_uuid: 公司UUID
        :return: 支付类型代码到名称的映射字典
        """
        query = select(PayType.pay_type_code, PayType.pay_type_name).where(
            and_(
                PayType.company_uuid == company_uuid,
                PayType.is_enabled == 1
            )
        )
        result = await db.execute(query)
        pay_types = result.fetchall()

        # 构建映射字典
        mapping = {}
        for pay_type_code, pay_type_name in pay_types:
            mapping[pay_type_code] = pay_type_name

        return mapping

    @classmethod
    async def get_template_product_skus_dao(cls, db: AsyncSession, product_ids: list):
        """
        获取模板产品SKU数据

        :param db: orm对象
        :param product_ids: 产品ID列表
        :return: 模板产品SKU列表
        """
        query = select(ProductSku).where(ProductSku.productid.in_(product_ids))
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def batch_add_products_dao(cls, db: AsyncSession, products_data: list):
        """
        批量新增产品数据库操作

        :param db: orm对象
        :param products_data: 产品数据列表
        :return: 新增的产品对象列表
        """
        product_objects = []
        for product_data in products_data:
            db_product = Product(**product_data)
            db.add(db_product)
            product_objects.append(db_product)

        await db.flush()
        return product_objects

    @classmethod
    async def batch_add_product_skus_dao(cls, db: AsyncSession, skus_data: list):
        """
        批量新增产品SKU数据库操作

        :param db: orm对象
        :param skus_data: 产品SKU数据列表
        :return: 新增的产品SKU对象列表
        """
        sku_objects = []
        for sku_data in skus_data:
            db_sku = ProductSku(**sku_data)
            db.add(db_sku)
            sku_objects.append(db_sku)

        await db.flush()
        return sku_objects

    @classmethod
    async def get_available_versions_dao(cls, db: AsyncSession):
        """
        获取可用的软件版本列表

        :param db: orm对象
        :return: 可用版本列表
        """
        try:
            from utils.log_util import logger
            logger.info("开始查询可用的软件版本列表")

            query = select(SoftwareVersion).where(SoftwareVersion.is_available == 1).order_by(SoftwareVersion.sort_order)
            result = await db.execute(query)
            versions = result.scalars().all()

            logger.info(f"成功查询到 {len(versions)} 个可用版本")
            return versions
        except Exception as e:
            from utils.log_util import logger
            logger.error(f"查询可用版本列表失败: {str(e)}")
            raise

    @classmethod
    async def get_company_versions_dao(cls, db: AsyncSession, company_id: str):
        """
        获取公司关联的版本信息（简化版）
        查询逻辑：company.id → company_version_relation.company_uuid → software_version.uuid → software_version.name

        :param db: orm对象
        :param company_id: 公司ID
        :return: 版本名称列表
        """
        query = select(
            SoftwareVersion.name
        ).select_from(
            CompanyVersionRelation.__table__.join(
                SoftwareVersion.__table__,
                CompanyVersionRelation.version_uuid == SoftwareVersion.uuid
            )
        ).where(
            and_(
                CompanyVersionRelation.company_uuid == company_id,
                CompanyVersionRelation.status == 1
            )
        )

        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def check_company_version_relation_dao(cls, db: AsyncSession, company_id: str, version_uuid: str):
        """
        检查公司版本关联是否已存在

        :param db: orm对象
        :param company_id: 公司ID
        :param version_uuid: 版本UUID
        :return: 关联记录
        """
        query = select(CompanyVersionRelation).where(
            and_(
                CompanyVersionRelation.company_uuid == company_id,
                CompanyVersionRelation.version_uuid == version_uuid,
                CompanyVersionRelation.status == 1
            )
        )
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_company_versions_detail_dao(cls, db: AsyncSession, company_id: str):
        """
        获取公司版本详细信息（包含到期时间）

        :param db: orm对象
        :param company_id: 公司ID
        :return: 版本详细信息列表
        """
        query = select(
            SoftwareVersion.uuid,
            SoftwareVersion.name,
            SoftwareVersion.description,
            SoftwareVersion.price,
            CompanyVersionRelation.expire_time,
            CompanyVersionRelation.purchase_time,
            CompanyVersionRelation.status
        ).select_from(
            CompanyVersionRelation.__table__.join(
                SoftwareVersion.__table__,
                CompanyVersionRelation.version_uuid == SoftwareVersion.uuid
            )
        ).where(
            and_(
                CompanyVersionRelation.company_uuid == company_id,
                CompanyVersionRelation.status == 1
            )
        ).order_by(SoftwareVersion.name)

        result = await db.execute(query)
        return result.fetchall()

    @classmethod
    async def update_company_version_expire_time_dao(cls, db: AsyncSession, company_id: str, version_uuid: str, expire_time, update_by: str):
        """
        更新公司版本到期时间

        :param db: orm对象
        :param company_id: 公司ID
        :param version_uuid: 版本UUID
        :param expire_time: 新的到期时间
        :param update_by: 更新者
        :return: 更新结果
        """
        from datetime import datetime

        query = update(CompanyVersionRelation).where(
            and_(
                CompanyVersionRelation.company_uuid == company_id,
                CompanyVersionRelation.version_uuid == version_uuid,
                CompanyVersionRelation.status == 1
            )
        ).values(
            expire_time=expire_time,
            update_time=datetime.now(),
            update_by=update_by
        )

        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    @classmethod
    async def get_companies_versions_for_batch_renew_dao(cls, db: AsyncSession, company_ids: list):
        """
        获取多个公司的版本信息，用于批量续费

        :param db: orm对象
        :param company_ids: 公司ID列表
        :return: 公司版本信息列表
        """
        query = select(
            CompanyVersionRelation.company_uuid,
            CompanyVersionRelation.version_uuid,
            CompanyVersionRelation.expire_time,
            SoftwareVersion.name.label('version_name'),
            Company.name.label('company_name')
        ).select_from(
            CompanyVersionRelation.__table__.join(
                SoftwareVersion.__table__,
                CompanyVersionRelation.version_uuid == SoftwareVersion.uuid
            ).join(
                Company.__table__,
                CompanyVersionRelation.company_uuid == Company.id
            )
        ).where(
            and_(
                CompanyVersionRelation.company_uuid.in_(company_ids),
                CompanyVersionRelation.status == 1,
                Company.is_delete == 0
            )
        ).order_by(Company.name, SoftwareVersion.name)

        result = await db.execute(query)
        return result.fetchall()

    @classmethod
    async def batch_update_company_versions_expire_time_dao(cls, db: AsyncSession, update_data: list, update_by: str):
        """
        批量更新公司版本到期时间

        :param db: orm对象
        :param update_data: 更新数据列表 [{'company_uuid': 'xxx', 'version_uuid': 'xxx', 'new_expire_time': datetime}]
        :param update_by: 更新者
        :return: 更新结果统计
        """
        from datetime import datetime

        success_count = 0
        failed_details = []

        for item in update_data:
            try:
                query = update(CompanyVersionRelation).where(
                    and_(
                        CompanyVersionRelation.company_uuid == item['company_uuid'],
                        CompanyVersionRelation.version_uuid == item['version_uuid'],
                        CompanyVersionRelation.status == 1
                    )
                ).values(
                    expire_time=item['new_expire_time'],
                    update_time=datetime.now(),
                    update_by=update_by
                )

                result = await db.execute(query)
                if result.rowcount > 0:
                    success_count += 1
                else:
                    failed_details.append({
                        'company_uuid': item['company_uuid'],
                        'version_uuid': item['version_uuid'],
                        'error': '未找到对应的版本关联记录'
                    })
            except Exception as e:
                failed_details.append({
                    'company_uuid': item['company_uuid'],
                    'version_uuid': item['version_uuid'],
                    'error': str(e)
                })

        return {
            'success_count': success_count,
            'failed_count': len(failed_details),
            'failed_details': failed_details
        }

    @classmethod
    async def add_batch_operation_log_dao(cls, db: AsyncSession, log_data: dict):
        """
        添加批量操作日志

        :param db: orm对象
        :param log_data: 日志数据
        :return: 日志记录对象
        """
        db_log = BatchOperationLog(**log_data)
        db.add(db_log)
        await db.flush()
        return db_log

    @classmethod
    async def get_batch_operation_log_dao(cls, db: AsyncSession, operation_id: str):
        """
        获取批量操作日志

        :param db: orm对象
        :param operation_id: 操作ID
        :return: 日志记录
        """
        query = select(BatchOperationLog).where(BatchOperationLog.operation_id == operation_id)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def update_batch_operation_log_status_dao(cls, db: AsyncSession, operation_id: str, status: int, update_by: str):
        """
        更新批量操作日志状态

        :param db: orm对象
        :param operation_id: 操作ID
        :param status: 新状态
        :param update_by: 更新者
        :return: 更新结果
        """
        from datetime import datetime

        query = update(BatchOperationLog).where(
            BatchOperationLog.operation_id == operation_id
        ).values(
            status=status,
            update_time=datetime.now(),
            update_by=update_by
        )

        result = await db.execute(query)
        return result.rowcount
