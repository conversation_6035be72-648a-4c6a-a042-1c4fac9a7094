from datetime import datetime, time
from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.experience_table_do import ExperienceTable
from module_admin.entity.vo.experience_table_vo import ExperienceTableModel, ExperienceTablePageQueryModel, ExperienceTableQueryModel
from utils.page_util import PageUtil


class ExperienceTableDao:
    """
    商户管理模块数据库操作层
    """

    @classmethod
    async def get_experience_table_detail_by_id(cls, db: AsyncSession, id: int):
        """
        根据商户id获取商户详细信息

        :param db: orm对象
        :param id: 商户id
        :return: 商户信息对象
        """
        experience_table_info = (await db.execute(select(ExperienceTable).where(ExperienceTable.id == id))).scalars().first()

        return experience_table_info

    @classmethod
    async def get_experience_table_list(cls, db: AsyncSession, query_object: ExperienceTablePageQueryModel, is_page: bool = False, filter_status: bool = True):
        """
        根据查询参数获取商户列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :param filter_status: 是否根据status进行筛选
        :return: 商户列表信息对象
        """
        query = (
            select(ExperienceTable)
            .where(
                ExperienceTable.company_name.like(f'%{query_object.company_name}%')
                if query_object.company_name
                else True,
                ExperienceTable.license_name.like(f'%{query_object.license_name}%')
                if query_object.license_name
                else True,
                ExperienceTable.license_code.like(f'%{query_object.license_code}%')
                if query_object.license_code
                else True,
                ExperienceTable.legal_person_name.like(f'%{query_object.legal_person_name}%')
                if query_object.legal_person_name
                else True,
                ExperienceTable.phone_number.like(f'%{query_object.phone_number}%')
                if query_object.phone_number
                else True,
                ExperienceTable.license_expiry_date.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
                (ExperienceTable.status == query_object.status if query_object.status is not None else True) if filter_status else True,
            )
            .order_by(ExperienceTable.id.desc())  # 按照ID降序排列，最新的记录在最前面
        )
        experience_table_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return experience_table_list

    @classmethod
    async def add_experience_table_dao(cls, db: AsyncSession, experience_table: ExperienceTableModel):
        """
        新增商户数据库操作

        :param db: orm对象
        :param experience_table: 商户对象
        :return:
        """
        db_experience_table = ExperienceTable(**experience_table.model_dump())
        db.add(db_experience_table)
        await db.flush()

        return db_experience_table

    @classmethod
    async def edit_experience_table_dao(cls, db: AsyncSession, experience_table: dict):
        """
        编辑商户数据库操作

        :param db: orm对象
        :param experience_table: 需要更新的商户字典
        :return:
        """
        await db.execute(update(ExperienceTable), [experience_table])

    @classmethod
    async def delete_experience_table_dao(cls, db: AsyncSession, experience_table: ExperienceTableModel):
        """
        删除商户数据库操作

        :param db: orm对象
        :param experience_table: 商户对象
        :return:
        """
        await db.execute(delete(ExperienceTable).where(ExperienceTable.id.in_([experience_table.id])))

    @classmethod
    async def update_experience_table_status_dao(cls, db: AsyncSession, id: int, status: int):
        """
        更新商户状态数据库操作

        :param db: orm对象
        :param id: 商户ID
        :param status: 状态值
        :return:
        """
        await db.execute(update(ExperienceTable).where(ExperienceTable.id == id).values(status=status))
