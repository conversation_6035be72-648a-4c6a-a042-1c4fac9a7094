from datetime import datetime
from sqlalchemy import select, and_, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.company_withdrawal_do import CompanyWithdrawal
from module_admin.entity.vo.company_withdrawal_vo import CompanyWithdrawalPageQueryModel, CompanyWithdrawalQueryModel
from utils.page_util import PageUtil


class CompanyWithdrawalDao:
    """
    公司提现申请数据库操作层
    """

    @classmethod
    async def add_company_withdrawal_dao(cls, db: AsyncSession, withdrawal_data: dict):
        """
        新增公司提现申请数据库操作

        :param db: orm对象
        :param withdrawal_data: 提现申请数据字典
        :return: 新增的提现申请对象
        """
        db_withdrawal = CompanyWithdrawal(**withdrawal_data)
        db.add(db_withdrawal)
        await db.flush()

        return db_withdrawal

    @classmethod
    async def get_company_withdrawal_list(cls, db: AsyncSession, query_object: CompanyWithdrawalPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取公司提现申请列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司提现申请列表
        """
        query = select(CompanyWithdrawal)

        # 构建查询条件
        conditions = []

        # 如果指定了ids参数，优先使用ids查询
        if hasattr(query_object, 'ids') and query_object.ids:
            id_list = [int(id_str.strip()) for id_str in query_object.ids.split(',') if id_str.strip()]
            if id_list:
                conditions.append(CompanyWithdrawal.id.in_(id_list))
        else:
            # 常规查询条件
            if query_object.company_uuid:
                conditions.append(CompanyWithdrawal.company_uuid == query_object.company_uuid)
            if query_object.withdrawal_no:
                conditions.append(CompanyWithdrawal.withdrawal_no.like(f'%{query_object.withdrawal_no}%'))
            if query_object.withdrawal_type:
                conditions.append(CompanyWithdrawal.withdrawal_type == query_object.withdrawal_type)
            if query_object.status:
                conditions.append(CompanyWithdrawal.status == query_object.status)
            if query_object.applicant_name:
                conditions.append(CompanyWithdrawal.applicant_name.like(f'%{query_object.applicant_name}%'))
            if query_object.reviewer_name:
                conditions.append(CompanyWithdrawal.reviewer_name.like(f'%{query_object.reviewer_name}%'))
            if query_object.processor_name:
                conditions.append(CompanyWithdrawal.processor_name.like(f'%{query_object.processor_name}%'))
            if query_object.begin_time:
                conditions.append(CompanyWithdrawal.apply_time >= query_object.begin_time)
            if query_object.end_time:
                conditions.append(CompanyWithdrawal.apply_time <= query_object.end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 按申请时间倒序排列
        query = query.order_by(CompanyWithdrawal.apply_time.desc())

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_company_withdrawal_detail_by_id(cls, db: AsyncSession, withdrawal_id: int):
        """
        根据提现申请ID获取详细信息

        :param db: orm对象
        :param withdrawal_id: 提现申请ID
        :return: 提现申请详细信息
        """
        query = select(CompanyWithdrawal).where(CompanyWithdrawal.id == withdrawal_id)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_company_withdrawal_by_no(cls, db: AsyncSession, withdrawal_no: str):
        """
        根据提现申请单号获取详细信息

        :param db: orm对象
        :param withdrawal_no: 提现申请单号
        :return: 提现申请详细信息
        """
        query = select(CompanyWithdrawal).where(CompanyWithdrawal.withdrawal_no == withdrawal_no)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def update_withdrawal_status(cls, db: AsyncSession, withdrawal_id: int, status: str, update_data: dict):
        """
        更新提现申请状态

        :param db: orm对象
        :param withdrawal_id: 提现申请ID
        :param status: 新状态
        :param update_data: 更新数据
        :return: 更新结果
        """
        update_data['status'] = status
        update_data['updated_at'] = datetime.now()
        
        query = update(CompanyWithdrawal).where(CompanyWithdrawal.id == withdrawal_id).values(**update_data)
        result = await db.execute(query)
        return result.rowcount > 0

    @classmethod
    async def get_company_pending_withdrawals(cls, db: AsyncSession, company_uuid: str):
        """
        获取公司待处理的提现申请

        :param db: orm对象
        :param company_uuid: 公司UUID
        :return: 待处理的提现申请列表
        """
        query = select(CompanyWithdrawal).where(
            and_(
                CompanyWithdrawal.company_uuid == company_uuid,
                CompanyWithdrawal.status.in_(['PENDING', 'APPROVED', 'PROCESSING'])
            )
        ).order_by(CompanyWithdrawal.apply_time.desc())
        
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_company_withdrawal_statistics(cls, db: AsyncSession, company_uuid: str = None):
        """
        获取提现申请统计信息

        :param db: orm对象
        :param company_uuid: 公司UUID（可选）
        :return: 统计信息
        """
        from sqlalchemy import func
        
        query = select(
            CompanyWithdrawal.status,
            func.count(CompanyWithdrawal.id).label('count'),
            func.sum(CompanyWithdrawal.apply_amount).label('total_amount')
        )
        
        if company_uuid:
            query = query.where(CompanyWithdrawal.company_uuid == company_uuid)
            
        query = query.group_by(CompanyWithdrawal.status)
        
        result = await db.execute(query)
        return result.fetchall()
