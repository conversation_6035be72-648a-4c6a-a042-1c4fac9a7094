from datetime import datetime
from sqlalchemy import select, and_, or_, update, not_, func
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.company_transaction_do import CompanyTransaction
from module_admin.entity.vo.company_transaction_vo import CompanyTransactionPageQueryModel, CompanyTransactionStatisticsQueryModel, CompanyTransactionQueryModel
from utils.page_util import PageUtil


class CompanyTransactionDao:
    """
    公司资金流水数据库操作层
    """

    @classmethod
    async def add_company_transaction_dao(cls, db: AsyncSession, transaction_data: dict):
        """
        新增公司资金流水数据库操作

        :param db: orm对象
        :param transaction_data: 流水数据字典
        :return: 新增的流水对象
        """
        db_transaction = CompanyTransaction(**transaction_data)
        db.add(db_transaction)
        await db.flush()

        return db_transaction

    @classmethod
    async def get_company_transactions(cls, db: AsyncSession, company_uuid: str, limit: int = 10):
        """
        获取公司资金流水记录

        :param db: orm对象
        :param company_uuid: 公司UUID
        :param limit: 查询条数限制
        :return: 流水记录列表
        """
        query = select(CompanyTransaction).where(
            CompanyTransaction.company_uuid == company_uuid
        ).order_by(CompanyTransaction.transaction_time.desc()).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_company_transaction_list(cls, db: AsyncSession, query_object: CompanyTransactionPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取公司资金流水列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司资金流水列表
        """
        query = select(CompanyTransaction)

        # 构建查询条件
        conditions = []

        # 如果指定了ids参数，优先使用ids查询
        if hasattr(query_object, 'ids') and query_object.ids:
            id_list = [int(id_str.strip()) for id_str in query_object.ids.split(',') if id_str.strip()]
            if id_list:
                conditions.append(CompanyTransaction.id.in_(id_list))
        else:
            # 常规查询条件
            if query_object.company_uuid:
                conditions.append(CompanyTransaction.company_uuid == query_object.company_uuid)
            if query_object.transaction_no:
                conditions.append(CompanyTransaction.transaction_no.like(f'%{query_object.transaction_no}%'))
            if query_object.business_type:
                conditions.append(CompanyTransaction.business_type == query_object.business_type)
            # 处理排除业务类型
            if hasattr(query_object, 'exclude_business_types') and query_object.exclude_business_types:
                exclude_types = [bt.strip() for bt in query_object.exclude_business_types.split(',') if bt.strip()]
                if exclude_types:
                    conditions.append(not_(CompanyTransaction.business_type.in_(exclude_types)))
            if query_object.transaction_type:
                conditions.append(CompanyTransaction.transaction_type == query_object.transaction_type)
            if query_object.operator_name:
                conditions.append(CompanyTransaction.operator_name.like(f'%{query_object.operator_name}%'))
            if query_object.transaction_status:
                conditions.append(CompanyTransaction.transaction_status == query_object.transaction_status)
            if query_object.begin_time:
                conditions.append(CompanyTransaction.transaction_time >= query_object.begin_time)
            if query_object.end_time:
                conditions.append(CompanyTransaction.transaction_time <= query_object.end_time)

        if conditions:
            query = query.where(and_(*conditions))

        # 按交易时间倒序排列
        query = query.order_by(CompanyTransaction.transaction_time.desc())

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_company_transaction_detail_by_id(cls, db: AsyncSession, transaction_id: int):
        """
        根据流水ID获取详细信息

        :param db: orm对象
        :param transaction_id: 流水ID
        :return: 流水详细信息
        """
        query = select(CompanyTransaction).where(CompanyTransaction.id == transaction_id)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_company_transactions_by_ids(cls, db: AsyncSession, transaction_ids: list):
        """
        根据流水ID列表批量获取资金流水记录

        :param db: orm对象
        :param transaction_ids: 流水ID列表
        :return: 资金流水记录列表
        """
        if not transaction_ids:
            return []

        query = select(CompanyTransaction).where(
            CompanyTransaction.id.in_(transaction_ids)
        ).order_by(CompanyTransaction.transaction_time.desc())

        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_recharge_transaction_list(cls, db: AsyncSession, query_object, is_page: bool = False):
        """
        获取充值管理列表（筛选pay_type为BALANCE的记录）

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 充值记录列表
        """
        query = select(CompanyTransaction)

        # 构建查询条件
        conditions = [CompanyTransaction.transaction_type == 1]  # 固定筛选收入订单（充值）

        # 如果指定了ids参数，优先使用ids查询
        if hasattr(query_object, 'ids') and query_object.ids:
            id_list = [int(id_str.strip()) for id_str in query_object.ids.split(',') if id_str.strip()]
            if id_list:
                conditions.append(CompanyTransaction.id.in_(id_list))
        else:
            # 常规查询条件
            if query_object.company_uuid:
                conditions.append(CompanyTransaction.company_uuid == query_object.company_uuid)
            if query_object.transaction_no:
                conditions.append(CompanyTransaction.transaction_no.like(f'%{query_object.transaction_no}%'))
            if query_object.business_type:
                conditions.append(CompanyTransaction.business_type == query_object.business_type)
            if query_object.transaction_type:
                conditions.append(CompanyTransaction.transaction_type == query_object.transaction_type)
            if query_object.operator_name:
                conditions.append(CompanyTransaction.operator_name.like(f'%{query_object.operator_name}%'))
            if query_object.transaction_status:
                conditions.append(CompanyTransaction.transaction_status == query_object.transaction_status)
            if query_object.begin_time:
                begin_datetime = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
                conditions.append(CompanyTransaction.transaction_time >= begin_datetime)
            if query_object.end_time:
                end_datetime = datetime.strptime(f'{query_object.end_time} 23:59:59', '%Y-%m-%d %H:%M:%S')
                conditions.append(CompanyTransaction.transaction_time <= end_datetime)

        if conditions:
            query = query.where(and_(*conditions))

        # 按交易时间倒序排列
        query = query.order_by(CompanyTransaction.transaction_time.desc())

        if is_page:
            # 分页查询
            return await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, True)
        else:
            # 不分页查询
            result = await db.execute(query)
            return result.scalars().all()

    @classmethod
    async def get_company_transaction_by_related_order_no(cls, db: AsyncSession, related_order_no: str):
        """
        根据关联订单号获取资金流水记录

        :param db: orm对象
        :param related_order_no: 关联订单号
        :return: 资金流水记录
        """
        query = select(CompanyTransaction).where(CompanyTransaction.related_order_no == related_order_no)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_company_transactions_by_related_order_no(cls, db: AsyncSession, related_order_no: str):
        """
        根据关联订单号获取所有相关的资金流水记录

        :param db: orm对象
        :param related_order_no: 关联订单号
        :return: 资金流水记录列表
        """
        query = select(CompanyTransaction).where(CompanyTransaction.related_order_no == related_order_no)
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def update_transaction_status(cls, db: AsyncSession, transaction_id: int, status: str):
        """
        更新资金流水状态

        :param db: orm对象
        :param transaction_id: 流水ID
        :param status: 新状态
        :return: 更新结果
        """
        query = update(CompanyTransaction).where(CompanyTransaction.id == transaction_id).values(transaction_status=status)
        result = await db.execute(query)
        return result.rowcount > 0

    @classmethod
    async def update_transaction_status_and_info(cls, db: AsyncSession, transaction_id: int, status: str, **kwargs):
        """
        更新资金流水状态和其他信息

        :param db: orm对象
        :param transaction_id: 流水ID
        :param status: 新状态
        :param kwargs: 其他要更新的字段
        :return: 更新结果
        """
        update_data = {'transaction_status': status}
        update_data.update(kwargs)

        query = update(CompanyTransaction).where(CompanyTransaction.id == transaction_id).values(**update_data)
        result = await db.execute(query)
        return result.rowcount > 0

    @classmethod
    async def update_transaction_withdrawal_no(cls, db: AsyncSession, transaction_id: int, withdrawal_no: str = None):
        """
        更新资金流水的提现申请单号绑定

        :param db: orm对象
        :param transaction_id: 流水ID
        :param withdrawal_no: 提现申请单号，None表示清空绑定
        :return: 更新结果
        """
        query = update(CompanyTransaction).where(CompanyTransaction.id == transaction_id)
        query = query.values(withdrawal_no=withdrawal_no)
        result = await db.execute(query)
        return result.rowcount > 0

    @classmethod
    async def get_transactions_by_withdrawal_no(cls, db: AsyncSession, withdrawal_no: str):
        """
        根据提现申请单号获取绑定的资金流水记录

        :param db: orm对象
        :param withdrawal_no: 提现申请单号
        :return: 资金流水记录列表
        """
        query = select(CompanyTransaction).where(CompanyTransaction.withdrawal_no == withdrawal_no)
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def clear_withdrawal_no_by_related_order(cls, db: AsyncSession, related_order_no: str):
        """
        根据关联订单号清空提现申请单号绑定

        :param db: orm对象
        :param related_order_no: 关联订单号
        :return: 更新结果
        """
        query = update(CompanyTransaction).where(CompanyTransaction.related_order_no == related_order_no)
        query = query.values(withdrawal_no=None)
        result = await db.execute(query)
        return result.rowcount > 0

    @classmethod
    async def get_company_transaction_statistics(cls, db: AsyncSession, query_object: CompanyTransactionStatisticsQueryModel):
        """
        获取公司资金流水统计数据

        :param db: orm对象
        :param query_object: 统计查询参数对象
        :return: 统计数据
        """
        # 构建统计查询
        query = select(
            func.sum(CompanyTransaction.amount).label('total_amount')
        )

        # 构建查询条件
        conditions = []

        # 业务类型条件
        if query_object.business_type:
            conditions.append(CompanyTransaction.business_type == query_object.business_type)

        # 处理排除业务类型
        if query_object.exclude_business_types:
            exclude_types = [bt.strip() for bt in query_object.exclude_business_types.split(',') if bt.strip()]
            if exclude_types:
                conditions.append(not_(CompanyTransaction.business_type.in_(exclude_types)))

        # 交易类型条件
        if query_object.transaction_type:
            conditions.append(CompanyTransaction.transaction_type == query_object.transaction_type)

        # 公司UUID条件
        if query_object.company_uuid:
            conditions.append(CompanyTransaction.company_uuid == query_object.company_uuid)

        # 交易流水号条件
        if query_object.transaction_no:
            conditions.append(CompanyTransaction.transaction_no.like(f'%{query_object.transaction_no}%'))

        # 操作员名称条件
        if query_object.operator_name:
            conditions.append(CompanyTransaction.operator_name.like(f'%{query_object.operator_name}%'))

        # 时间范围条件
        if query_object.begin_time:
            conditions.append(CompanyTransaction.transaction_time >= query_object.begin_time)
        if query_object.end_time:
            conditions.append(CompanyTransaction.transaction_time <= query_object.end_time)

        # 交易状态条件
        if query_object.transaction_status:
            conditions.append(CompanyTransaction.transaction_status == query_object.transaction_status)
        else:
            # 如果没有指定交易状态，默认只统计成功的交易
            conditions.append(CompanyTransaction.transaction_status == 'SUCCESS')

        if conditions:
            query = query.where(and_(*conditions))

        result = await db.execute(query)
        row = result.first()

        return {
            'total_amount': float(row.total_amount) if row.total_amount else 0.0
        }
