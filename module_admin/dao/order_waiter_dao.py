from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.order_waiter_do import OrderWaiter


class OrderWaiterDao:
    """
    订单派单记录数据库操作层
    """

    @classmethod
    async def get_order_waiter_by_order_number(cls, db: AsyncSession, order_number: str):
        """
        根据订单编号获取派单记录信息

        :param db: orm对象
        :param order_number: 订单编号
        :return: 派单记录信息对象列表
        """
        query_order_waiter_info = (
            await db.execute(
                select(OrderWaiter)
                .where(OrderWaiter.order_number == order_number)
                .order_by(OrderWaiter.create_time.desc())
            )
        ).scalars().all()

        return query_order_waiter_info

    @classmethod
    async def get_order_waiter_by_id(cls, db: AsyncSession, waiter_id: int):
        """
        根据派单记录ID获取派单记录详细信息

        :param db: orm对象
        :param waiter_id: 派单记录ID
        :return: 派单记录信息对象
        """
        query_order_waiter_info = (
            await db.execute(
                select(OrderWaiter)
                .where(OrderWaiter.id == waiter_id)
            )
        ).scalars().first()

        return query_order_waiter_info

    @classmethod
    async def update_order_waiter_staff(cls, db: AsyncSession, order_number: str, service_id: str, service_name: str):
        """
        更新订单派单记录的员工信息

        :param db: orm对象
        :param order_number: 订单编号
        :param service_id: 新的员工ID
        :param service_name: 新的员工姓名
        :return: 更新结果
        """
        await db.execute(
            update(OrderWaiter)
            .where(OrderWaiter.order_number == order_number)
            .values(service_id=service_id, service_name=service_name)
        )
