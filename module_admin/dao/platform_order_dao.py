from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.platform_order_do import PlatformOrder


class PlatformOrderDao:
    """
    平台订单数据库操作层
    """

    @classmethod
    async def add_platform_order_dao(cls, db: AsyncSession, order_data: dict):
        """
        新增平台订单数据库操作

        :param db: orm对象
        :param order_data: 订单数据字典
        :return: 新增的订单对象
        """
        db_order = PlatformOrder(**order_data)
        db.add(db_order)
        await db.flush()

        return db_order

    @classmethod
    async def get_platform_order_by_id(cls, db: AsyncSession, order_id: int):
        """
        根据订单ID获取订单详细信息

        :param db: orm对象
        :param order_id: 订单ID
        :return: 订单信息对象
        """
        query = select(PlatformOrder).where(PlatformOrder.id == order_id)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_platform_order_by_order_no(cls, db: AsyncSession, order_no: str):
        """
        根据订单号获取订单详细信息

        :param db: orm对象
        :param order_no: 订单号
        :return: 订单信息对象
        """
        query = select(PlatformOrder).where(PlatformOrder.order_no == order_no)
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def update_order_status(cls, db: AsyncSession, order_id: int, order_status: int, order_status_name: str):
        """
        更新订单状态

        :param db: orm对象
        :param order_id: 订单ID
        :param order_status: 订单状态
        :param order_status_name: 订单状态名称
        :return: 更新结果
        """
        query = update(PlatformOrder).where(
            PlatformOrder.id == order_id
        ).values(order_status=order_status, order_status_name=order_status_name)
        await db.execute(query)
