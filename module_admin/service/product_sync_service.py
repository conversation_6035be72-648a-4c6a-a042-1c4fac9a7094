from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.product_sync_dao import ProductSyncDao
from module_admin.entity.vo.product_sync_vo import ProductSyncPageQueryModel, ProductSyncExportModel
from module_admin.service.common_service import CommonService
from utils.common_util import export_list2excel
from exceptions.exception import ServiceException


class ProductSyncService:
    """
    产品同步管理模块服务层
    """

    @classmethod
    async def get_product_sync_list_services(cls, query_db: AsyncSession, query_object: ProductSyncPageQueryModel, is_page: bool = False):
        """
        获取产品同步列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 产品同步列表信息对象
        """
        product_sync_list_result = await ProductSyncDao.get_product_sync_list(query_db, query_object, is_page)
        return product_sync_list_result

    @classmethod
    async def product_sync_detail_services(cls, query_db: AsyncSession, product_id: int):
        """
        获取产品同步详细信息service

        :param query_db: orm对象
        :param product_id: 产品ID
        :return: 产品同步信息对象
        """
        product_sync = await ProductSyncDao.get_product_sync_detail_by_id(query_db, product_id)
        if product_sync:
            # 将查询结果转换为字典格式
            product_sync_dict = {
                'id': product_sync.id,
                'product_name': product_sync.product_name,
                'company_uuid': product_sync.company_uuid,
                'company_id': product_sync.company_id,
                'company_name': product_sync.company_name or '',  # 确保即使为空也显示
                'service_skill_name': product_sync.service_skill_name,
                'service_skill_id': product_sync.service_skill_id,
                'img_id': product_sync.img_id,
                'serve_type_name': product_sync.serve_type_name,
                'online_store_num': product_sync.online_store_num,
                'sum_num': product_sync.sum_num,
                'type_name': product_sync.type_name,
                'product_status': product_sync.product_status,
                'is_delete': product_sync.is_delete,
                'details': product_sync.details,
                'min_number': product_sync.min_number,
                'max_number': product_sync.max_number,
                'create_time': product_sync.create_time,
                'update_time': product_sync.update_time
            }

            # 获取产品相关文件
            product_images = []
            product_detail_images = []
            product_videos = []

            # 获取产品图片 (img_id)
            if product_sync.img_id:
                try:
                    img_id = int(product_sync.img_id)
                    product_images = await CommonService.get_file_list_by_main_id(query_db, img_id)
                except (ValueError, TypeError):
                    pass

            # 获取产品详情图 (details作为ID)
            if product_sync.details:
                try:
                    details_id = int(product_sync.details)
                    product_detail_images = await CommonService.get_file_list_by_main_id(query_db, details_id)
                except (ValueError, TypeError):
                    pass

            # 获取产品视频 (video_id)
            if hasattr(product_sync, 'video_id') and product_sync.video_id:
                try:
                    video_id = int(product_sync.video_id)
                    product_videos = await CommonService.get_file_list_by_main_id(query_db, video_id)
                except (ValueError, TypeError):
                    pass

            # 添加文件信息到结果中
            product_sync_dict['productImages'] = product_images
            product_sync_dict['productDetailImages'] = product_detail_images
            product_sync_dict['productVideos'] = product_videos

            return product_sync_dict
        else:
            raise ServiceException(message='产品不存在')

    @classmethod
    async def export_product_sync_list_services(cls, query_db: AsyncSession, query_object: ProductSyncExportModel):
        """
        导出产品同步列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :return: 产品同步列表excel对象
        """
        product_sync_export_result = await ProductSyncDao.get_product_sync_export_list(query_db, query_object)
        
        # 转换数据格式用于导出
        export_data = []
        for row in product_sync_export_result:
            export_data.append({
                '产品ID': row.id,
                '产品昵称': row.product_name or '',
                '门店ID': row.company_id or '',
                '公司昵称': row.company_name or '',
                '产品类型': row.service_skill_name or '',
                '服务技能ID': row.service_skill_id or '',
                '在线门店数量': row.online_store_num or 0,
                '总数量': row.sum_num or 0,
                '类型名称': row.type_name or '',
                '产品状态': cls._get_product_status_text(row.product_status),
                '创建时间': row.create_time.strftime('%Y-%m-%d %H:%M:%S') if row.create_time else '',
                '更新时间': row.update_time.strftime('%Y-%m-%d %H:%M:%S') if row.update_time else ''
            })

        # 生成Excel文件
        excel_file = export_list2excel(export_data, '产品同步列表')
        return excel_file

    @classmethod
    async def get_product_sync_statistics_services(cls, query_db: AsyncSession):
        """
        获取产品同步统计信息service

        :param query_db: orm对象
        :return: 统计信息对象
        """
        try:
            statistics_result = await ProductSyncDao.get_product_sync_statistics(query_db)

            # 处理统计数据，添加状态文本描述
            processed_stats = {
                'total_products': statistics_result.get('total_products', 0),
                'linked_products': statistics_result.get('linked_products', 0),
                'unlinked_products': statistics_result.get('unlinked_products', 0),
                'status_stats': []
            }

            # 转换状态统计数据
            status_stats = statistics_result.get('status_stats', {})
            for status, count in status_stats.items():
                processed_stats['status_stats'].append({
                    'status': status,
                    'status_text': cls._get_product_status_text(status),
                    'count': count
                })

            return processed_stats
        except Exception as e:
            # 如果统计查询失败，返回默认值
            return {
                'total_products': 0,
                'linked_products': 0,
                'unlinked_products': 0,
                'status_stats': []
            }

    @staticmethod
    def _get_product_status_text(status):
        """
        获取产品状态文本描述

        :param status: 产品状态码
        :return: 状态文本描述
        """
        status_map = {
            0: '正常',
            1: '停用',
            2: '待审核',
            3: '审核失败'
        }
        return status_map.get(status, '未知状态')

    @classmethod
    async def sync_product_data_services(cls, query_db: AsyncSession, product_ids: list):
        """
        同步产品数据service

        :param query_db: orm对象
        :param product_ids: 需要同步的产品ID列表
        :return: 同步结果
        """
        # 这里可以实现具体的产品数据同步逻辑
        # 例如：更新产品状态、同步到其他系统等
        
        success_count = 0
        failed_count = 0
        failed_products = []
        
        for product_id in product_ids:
            try:
                # 获取产品详情
                product_detail = await ProductSyncDao.get_product_sync_detail_by_id(query_db, product_id)
                if product_detail:
                    # 执行同步逻辑（这里可以根据实际需求实现）
                    # 例如：调用第三方API、更新数据库状态等
                    success_count += 1
                else:
                    failed_count += 1
                    failed_products.append(f'产品ID {product_id} 不存在')
            except Exception as e:
                failed_count += 1
                failed_products.append(f'产品ID {product_id} 同步失败: {str(e)}')
        
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'failed_products': failed_products,
            'message': f'同步完成，成功 {success_count} 个，失败 {failed_count} 个'
        }
