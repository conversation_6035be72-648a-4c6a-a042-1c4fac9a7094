import json
from datetime import datetime
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.company_withdrawal_dao import CompanyWithdrawalDao
from module_admin.dao.company_dao import CompanyDao
from module_admin.dao.company_transaction_dao import CompanyTransactionDao
from module_admin.entity.vo.company_withdrawal_vo import (
    CompanyWithdrawalPageQueryModel,
    CompanyWithdrawalCreateModel,
    CompanyWithdrawalReviewModel,
    CompanyWithdrawalProcessModel,
    CompanyWithdrawalResponseModel
)
from utils.common_util import CommonUtil, CamelCaseUtil


class CompanyWithdrawalService:
    """
    公司提现申请服务层
    """

    @classmethod
    async def get_company_withdrawal_list_services(
        cls,
        query_db: AsyncSession,
        query_object: CompanyWithdrawalPageQueryModel,
        is_page: bool = False
    ):
        """
        获取公司提现申请列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司提现申请列表信息对象
        """
        withdrawal_list_result = await CompanyWithdrawalDao.get_company_withdrawal_list(
            query_db, query_object, is_page
        )

        # 获取所有涉及的公司UUID，用于批量获取公司名称
        company_uuids = set()

        if is_page:
            # 分页结果处理 - PageUtil已经转换为字典格式
            if withdrawal_list_result.rows:
                for item_dict in withdrawal_list_result.rows:
                    company_uuids.add(item_dict.get('companyUuid'))
        else:
            # 非分页结果处理 - 需要手动转换格式
            if withdrawal_list_result:
                for item in withdrawal_list_result:
                    company_uuids.add(item.company_uuid)

        # 批量获取公司名称映射
        company_name_mapping = {}
        for company_uuid in company_uuids:
            if company_uuid:
                company = await CompanyDao.get_company_by_id(query_db, company_uuid)
                if company:
                    company_name_mapping[company_uuid] = company.name

        if is_page:
            # 分页结果处理 - PageUtil已经转换为字典格式
            if withdrawal_list_result.rows:
                # 添加状态名称和公司名称
                for item_dict in withdrawal_list_result.rows:
                    item_dict['statusName'] = cls._get_status_name(item_dict.get('status'))
                    item_dict['withdrawalTypeName'] = cls._get_withdrawal_type_name(item_dict.get('withdrawalType'))
                    item_dict['companyName'] = company_name_mapping.get(item_dict.get('companyUuid'), '未知公司')
        else:
            # 非分页结果处理 - 需要手动转换格式
            if withdrawal_list_result:
                converted_data = []
                for item in withdrawal_list_result:
                    item_dict = CamelCaseUtil.transform_result([item])[0]
                    # 添加状态名称和公司名称
                    item_dict['statusName'] = cls._get_status_name(item_dict.get('status'))
                    item_dict['withdrawalTypeName'] = cls._get_withdrawal_type_name(item_dict.get('withdrawalType'))
                    item_dict['companyName'] = company_name_mapping.get(item_dict.get('companyUuid'), '未知公司')
                    converted_data.append(item_dict)
                withdrawal_list_result = converted_data

        return withdrawal_list_result

    @classmethod
    async def get_company_withdrawal_detail_services(cls, query_db: AsyncSession, withdrawal_id: int):
        """
        获取公司提现申请详情信息service

        :param query_db: orm对象
        :param withdrawal_id: 提现申请ID
        :return: 公司提现申请详情信息对象
        """
        withdrawal_detail = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)

        if withdrawal_detail:
            # 转换数据格式
            detail_dict = CamelCaseUtil.transform_result([withdrawal_detail])[0]
            # 添加状态名称和提现类型名称
            detail_dict['statusName'] = cls._get_status_name(detail_dict.get('status'))
            detail_dict['withdrawalTypeName'] = cls._get_withdrawal_type_name(detail_dict.get('withdrawalType'))

            # 添加公司名称
            company_uuid = detail_dict.get('companyUuid')
            if company_uuid:
                company = await CompanyDao.get_company_by_id(query_db, company_uuid)
                detail_dict['companyName'] = company.name if company else '未知公司'

            # 注意：不再在后端处理sourceTransactions，直接返回sourceTransactionIds给前端处理

            return detail_dict

        return None

    @classmethod
    async def create_withdrawal_application_services(
        cls, 
        query_db: AsyncSession, 
        withdrawal_data: CompanyWithdrawalCreateModel, 
        current_user
    ):
        """
        创建提现申请service

        :param query_db: orm对象
        :param withdrawal_data: 提现申请数据
        :param current_user: 当前用户
        :return: 创建结果
        """
        try:
            # 1. 检查公司是否存在
            company = await CompanyDao.get_company_by_id(query_db, withdrawal_data.company_uuid)
            if not company:
                raise ServiceException(message='公司不存在')

            # 2. 检查公司余额是否足够
            current_balance = await CompanyDao.get_company_balance(query_db, withdrawal_data.company_uuid)
            if current_balance < withdrawal_data.apply_amount:
                raise ServiceException(message=f'余额不足，当前余额：{current_balance}，申请金额：{withdrawal_data.apply_amount}')

            # 3. 检查是否有待处理的提现申请
            pending_withdrawals = await CompanyWithdrawalDao.get_company_pending_withdrawals(
                query_db, withdrawal_data.company_uuid
            )
            if pending_withdrawals:
                raise ServiceException(message='存在待处理的提现申请，请等待处理完成后再申请')

            # 4. 计算手续费和实际到账金额
            fee_rate = Decimal('0.1') if withdrawal_data.withdrawal_type == 2 else Decimal('0')  # 零工提现10%手续费
            fee_amount = withdrawal_data.apply_amount * fee_rate
            actual_amount = withdrawal_data.apply_amount - fee_amount

            # 5. 验证source_transaction_ids并检查流水记录
            source_transaction_ids = getattr(withdrawal_data, 'source_transaction_ids', None)
            if not source_transaction_ids:
                # 如果没有提供source_transaction_ids，暂时跳过验证（兼容旧版本）
                source_transaction_ids = ""
                transaction_ids = []
            else:
                source_transaction_ids = source_transaction_ids.strip()
                if not source_transaction_ids:
                    raise ServiceException(message='关联的资金流水记录ID不能为空')

                # 解析流水ID列表
                transaction_ids = [tid.strip() for tid in source_transaction_ids.split(',') if tid.strip()]
                if not transaction_ids:
                    raise ServiceException(message='关联的资金流水记录ID格式不正确')

                # 验证流水记录是否存在且属于该公司
                total_available_amount = Decimal('0')
                for transaction_id in transaction_ids:
                    try:
                        tid = int(transaction_id)
                        transaction = await CompanyTransactionDao.get_company_transaction_detail_by_id(query_db, tid)
                        if not transaction:
                            raise ServiceException(message=f'流水记录{transaction_id}不存在')
                        if transaction.company_uuid != withdrawal_data.company_uuid:
                            raise ServiceException(message=f'流水记录{transaction_id}不属于该公司')
                        # 检查withdrawal_no字段（如果存在）
                        if hasattr(transaction, 'withdrawal_no') and transaction.withdrawal_no:
                            raise ServiceException(message=f'流水记录{transaction_id}已绑定其他提现申请')
                        if transaction.transaction_status != 'SUCCESS':
                            raise ServiceException(message=f'流水记录{transaction_id}状态异常，无法用于提现')
                        # 只有收入类型的流水才能用于提现
                        if transaction.transaction_type != 1:  # 1:充值
                            raise ServiceException(message=f'流水记录{transaction_id}不是收入类型，无法用于提现')
                        total_available_amount += transaction.amount
                    except ValueError:
                        raise ServiceException(message=f'流水记录ID{transaction_id}格式不正确')

                # 验证申请金额不能超过关联流水的总金额
                if withdrawal_data.apply_amount > total_available_amount:
                    raise ServiceException(message=f'申请金额({withdrawal_data.apply_amount})超过关联流水总金额({total_available_amount})')

            # 6. 生成提现申请单号
            withdrawal_no = f"WD{datetime.now().strftime('%Y%m%d%H%M%S')}{CommonUtil.get_short_uuid(6)}"

            # 7. 准备提现申请数据
            withdrawal_create_data = {
                'company_uuid': withdrawal_data.company_uuid,
                'withdrawal_no': withdrawal_no,
                'withdrawal_type': withdrawal_data.withdrawal_type,
                'withdrawal_type_name': cls._get_withdrawal_type_name(withdrawal_data.withdrawal_type),
                'apply_amount': withdrawal_data.apply_amount,
                'fee_rate': fee_rate,
                'fee_amount': fee_amount,
                'actual_amount': actual_amount,
                'bank_name': withdrawal_data.bank_name,
                'bank_account': withdrawal_data.bank_account,
                'account_holder': withdrawal_data.account_holder,
                'invoice_info': json.dumps(withdrawal_data.invoice_info) if withdrawal_data.invoice_info else None,
                'apply_reason': withdrawal_data.apply_reason,
                'source_transaction_ids': source_transaction_ids,
                'status': 'PENDING',
                'applicant_id': current_user.user.user_id if current_user else 'system',
                'applicant_name': current_user.user.user_name if current_user else 'system',
                'apply_time': datetime.now(),
                'created_by': current_user.user.user_name if current_user else 'system',
                'remark': withdrawal_data.remark
            }

            # 7. 冻结申请金额
            await CompanyDao.freeze_company_funds(
                query_db, withdrawal_data.company_uuid, withdrawal_data.apply_amount
            )

            # 8. 创建提现申请
            withdrawal = await CompanyWithdrawalDao.add_company_withdrawal_dao(query_db, withdrawal_create_data)

            # 9. 绑定withdrawal_no到相关流水记录（如果有的话）
            if transaction_ids:
                try:
                    for transaction_id in transaction_ids:
                        tid = int(transaction_id)
                        await CompanyTransactionDao.update_transaction_withdrawal_no(query_db, tid, withdrawal_no)
                except Exception as e:
                    # 如果数据库字段还没有添加，忽略这个错误
                    print(f"Warning: withdrawal_no field may not exist in database: {e}")

            # 10. 提交事务
            await query_db.commit()

            # 9. 返回创建结果
            return {
                'withdrawal_id': withdrawal.id,
                'withdrawal_no': withdrawal_no,
                'apply_amount': withdrawal_data.apply_amount,
                'fee_amount': fee_amount,
                'actual_amount': actual_amount,
                'status': 'PENDING'
            }

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'创建提现申请失败: {str(e)}')

    @classmethod
    async def review_withdrawal_application_services(
        cls,
        query_db: AsyncSession,
        withdrawal_id: int,
        review_data: CompanyWithdrawalReviewModel,
        current_user
    ):
        """
        审核提现申请service

        :param query_db: orm对象
        :param withdrawal_id: 提现申请ID
        :param review_data: 审核数据
        :param current_user: 当前用户
        :return: 审核结果
        """
        try:
            # 1. 获取提现申请
            withdrawal = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)
            if not withdrawal:
                raise ServiceException(message='提现申请不存在')

            # 2. 检查状态是否可以审核
            if withdrawal.status != 'PENDING':
                raise ServiceException(message=f'当前状态({cls._get_status_name(withdrawal.status)})不允许审核')

            # 3. 准备更新数据
            new_status = 'APPROVED' if review_data.action == 'approve' else 'REJECTED'
            update_data = {
                'reviewer_id': current_user.user.user_id if current_user else 'system',
                'reviewer_name': current_user.user.user_name if current_user else 'system',
                'review_time': datetime.now(),
                'review_comment': review_data.review_comment,
                'updated_by': current_user.user.user_name if current_user else 'system'
            }

            # 4. 如果是拒绝操作，需要解冻资金并更新相关的资金流水状态
            if review_data.action == 'reject':
                # 解冻申请金额，返回到余额
                await CompanyDao.unfreeze_company_funds(
                    query_db, withdrawal.company_uuid, withdrawal.apply_amount, return_to_balance=True
                )

                # 查找并更新系统创建的WITHDRAWAL流水状态为失败
                withdrawal_transaction_records = await CompanyTransactionDao.get_company_transactions_by_related_order_no(
                    query_db, withdrawal.withdrawal_no
                )

                for transaction_record in withdrawal_transaction_records:
                    if transaction_record.business_type in ['WITHDRAWAL', 'TAX_DEDUCTION']:
                        await CompanyTransactionDao.update_transaction_status(
                            query_db, transaction_record.id, 'FAILED'
                        )

                # 清空原始收入流水的withdrawal_no绑定，方便客户端重新申请提现
                try:
                    bound_transaction_records = await CompanyTransactionDao.get_transactions_by_withdrawal_no(
                        query_db, withdrawal.withdrawal_no
                    )

                    for transaction_record in bound_transaction_records:
                        await CompanyTransactionDao.update_transaction_withdrawal_no(
                            query_db, transaction_record.id, None
                        )
                except Exception as e:
                    # 如果数据库字段还没有添加，忽略这个错误
                    print(f"Warning: withdrawal_no field may not exist in database: {e}")

            # 5. 更新提现申请状态
            success = await CompanyWithdrawalDao.update_withdrawal_status(
                query_db, withdrawal_id, new_status, update_data
            )

            if not success:
                raise ServiceException(message='审核失败')

            # 5. 提交事务
            await query_db.commit()

            return {
                'withdrawal_id': withdrawal_id,
                'status': new_status,
                'status_name': cls._get_status_name(new_status),
                'review_time': update_data['review_time']
            }

        except ServiceException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'审核提现申请失败: {str(e)}')

    @classmethod
    async def process_withdrawal_application_services(
        cls,
        query_db: AsyncSession,
        withdrawal_id: int,
        process_data: CompanyWithdrawalProcessModel,
        current_user
    ):
        """
        处理提现申请service

        :param query_db: orm对象
        :param withdrawal_id: 提现申请ID
        :param process_data: 处理数据
        :param current_user: 当前用户
        :return: 处理结果
        """
        # 用于回滚的变量
        company_uuid = None
        apply_amount = None
        withdrawal_transaction_records = []
        funds_deducted = False
        transactions_updated = False

        try:
            # 1. 获取提现申请
            withdrawal = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)
            if not withdrawal:
                raise ServiceException(message='提现申请不存在')

            # 2. 提前提取所有需要的属性值，避免在事务过程中访问ORM对象属性
            withdrawal_status = withdrawal.status
            withdrawal_no = withdrawal.withdrawal_no
            company_uuid = withdrawal.company_uuid
            apply_amount = withdrawal.apply_amount

            # 3. 检查状态是否可以处理
            if withdrawal_status not in ['APPROVED', 'PROCESSING']:
                raise ServiceException(message=f'当前状态({cls._get_status_name(withdrawal_status)})不允许处理')

            # 4. 根据处理动作进行不同的验证
            # 获取action字段，如果没有则默认为complete（向后兼容）
            action = getattr(process_data, 'action', 'complete')

            if action == 'complete':
                # 完成操作需要验证transaction_id
                if not process_data.transaction_id or not process_data.transaction_id.strip():
                    raise ServiceException(message='交易流水号不能为空')
            elif action == 'reject':
                # 驳回操作不需要transaction_id，但需要备注说明原因
                if not process_data.remark or not process_data.remark.strip():
                    raise ServiceException(message='驳回操作必须填写驳回原因')

            # 5. 检查是否已经处理过（防重复处理）
            if withdrawal_status == 'COMPLETED':
                raise ServiceException(message='该提现申请已经处理完成，请勿重复处理')

            # 6. 获取并验证客户端创建的流水记录
            withdrawal_transaction_records = await CompanyTransactionDao.get_company_transactions_by_related_order_no(
                query_db, withdrawal_no
            )

            if not withdrawal_transaction_records:
                raise ServiceException(message='未找到相关的交易流水记录，无法处理')

            # 验证流水状态
            withdrawal_record = None
            tax_record = None
            for record in withdrawal_transaction_records:
                if record.business_type == 'WITHDRAWAL':
                    withdrawal_record = record
                elif record.business_type == 'TAX_DEDUCTION':
                    tax_record = record

            if not withdrawal_record:
                raise ServiceException(message='未找到提现流水记录，无法处理')

            # 检查流水状态是否可以处理
            if withdrawal_record.transaction_status not in ['PENDING']:
                raise ServiceException(message=f'提现流水状态异常({withdrawal_record.transaction_status})，无法处理')

            if tax_record and tax_record.transaction_status not in ['PENDING']:
                raise ServiceException(message=f'税费流水状态异常({tax_record.transaction_status})，无法处理')

            # 7. 根据处理动作执行不同的逻辑
            if action == 'complete':
                # 完成处理逻辑
                # 检查公司冻结资金是否足够
                current_frozen = await CompanyDao.get_company_frozen_amount(query_db, company_uuid)
                if current_frozen < apply_amount:
                    raise ServiceException(message=f'冻结资金不足，当前冻结资金：{current_frozen}，申请金额：{apply_amount}')

                # 扣除冻结资金（不返回到余额，直接扣除）
                unfreeze_result = await CompanyDao.unfreeze_company_funds(
                    query_db, company_uuid, apply_amount, return_to_balance=False
                )
                funds_deducted = True

                # 更新系统创建的WITHDRAWAL和TAX_DEDUCTION流水状态为成功
                for transaction_record in withdrawal_transaction_records:
                    if transaction_record.business_type in ['WITHDRAWAL', 'TAX_DEDUCTION']:
                        await CompanyTransactionDao.update_transaction_status(
                            query_db, transaction_record.id, 'SUCCESS'
                        )
                transactions_updated = True

                # 更新提现申请状态为已完成
                update_data = {
                    'processor_id': current_user.user.user_id if current_user else 'system',
                    'processor_name': current_user.user.user_name if current_user else 'system',
                    'process_time': datetime.now(),
                    'completion_time': datetime.now(),
                    'transaction_id': process_data.transaction_id,
                    'updated_by': current_user.user.user_name if current_user else 'system'
                }

            elif action == 'reject':
                # 驳回处理逻辑
                # 解冻申请金额，返回到余额
                await CompanyDao.unfreeze_company_funds(
                    query_db, company_uuid, apply_amount, return_to_balance=True
                )

                # 更新系统创建的WITHDRAWAL和TAX_DEDUCTION流水状态为失败
                for transaction_record in withdrawal_transaction_records:
                    if transaction_record.business_type in ['WITHDRAWAL', 'TAX_DEDUCTION']:
                        await CompanyTransactionDao.update_transaction_status(
                            query_db, transaction_record.id, 'FAILED'
                        )

                # 清空原始收入流水的withdrawal_no绑定，方便客户端重新申请提现
                try:
                    bound_transaction_records = await CompanyTransactionDao.get_transactions_by_withdrawal_no(
                        query_db, withdrawal_no
                    )

                    for transaction_record in bound_transaction_records:
                        await CompanyTransactionDao.update_transaction_withdrawal_no(
                            query_db, transaction_record.id, None
                        )
                except Exception as e:
                    # 如果数据库字段还没有添加，忽略这个错误
                    print(f"Warning: withdrawal_no field may not exist in database: {e}")

                # 更新提现申请状态为处理驳回
                update_data = {
                    'processor_id': current_user.user.user_id if current_user else 'system',
                    'processor_name': current_user.user.user_name if current_user else 'system',
                    'process_time': datetime.now(),
                    'remark': process_data.remark,
                    'updated_by': current_user.user.user_name if current_user else 'system'
                }

            # 8. 根据action更新不同的状态
            if action == 'complete':
                if process_data.remark:
                    update_data['remark'] = process_data.remark

                success = await CompanyWithdrawalDao.update_withdrawal_status(
                    query_db, withdrawal_id, 'COMPLETED', update_data
                )
            else:  # reject
                success = await CompanyWithdrawalDao.update_withdrawal_status(
                    query_db, withdrawal_id, 'REJECTED_PROCESS', update_data
                )

            if not success:
                raise ServiceException(message='更新提现申请状态失败')

            # 9. 在提交事务前获取处理后的余额和必要信息
            final_balance = await CompanyDao.get_company_balance(query_db, company_uuid)

            # 在事务提交前获取所需的数据，避免事务提交后访问ORM对象属性
            result_data = {}
            if action == 'complete':
                # 在事务提交前获取transaction_no，避免事务提交后访问ORM对象属性
                transaction_no = withdrawal_record.transaction_no
                result_data = {
                    'withdrawal_id': withdrawal_id,
                    'status': 'COMPLETED',
                    'status_name': '已完成',
                    'transaction_no': transaction_no,
                    'balance_after': final_balance,
                    'frozen_after': unfreeze_result['new_frozen'],
                    'completion_time': update_data['completion_time']
                }
            else:  # reject
                result_data = {
                    'withdrawal_id': withdrawal_id,
                    'status': 'REJECTED_PROCESS',
                    'status_name': '处理驳回',
                    'balance_after': final_balance,
                    'process_time': update_data['process_time'],
                    'remark': process_data.remark
                }

            # 10. 提交事务
            await query_db.commit()

            # 11. 返回结果
            return result_data

        except Exception as e:
            # 统一的异常处理和回滚逻辑
            await cls._handle_process_exception(
                query_db, withdrawal_id, e,
                company_uuid, apply_amount,
                withdrawal_transaction_records,
                funds_deducted, transactions_updated
            )

    @classmethod
    async def _handle_process_exception(
        cls,
        query_db: AsyncSession,
        withdrawal_id: int,
        exception: Exception,
        company_uuid: str,
        apply_amount,
        withdrawal_transaction_records: list,
        funds_deducted: bool,
        transactions_updated: bool
    ):
        """
        统一处理提现申请处理过程中的异常和回滚

        :param query_db: 数据库会话
        :param withdrawal_id: 提现申请ID
        :param exception: 异常对象
        :param company_uuid: 公司UUID
        :param apply_amount: 申请金额
        :param withdrawal_transaction_records: 流水记录列表
        :param funds_deducted: 是否已扣除资金
        :param transactions_updated: 是否已更新流水状态
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 执行回滚操作
            if funds_deducted and company_uuid and apply_amount:
                # 检查当前冻结资金是否足够回滚
                try:
                    current_frozen = await CompanyDao.get_company_frozen_amount(query_db, company_uuid)
                    if current_frozen >= apply_amount:
                        # 回滚资金操作：将扣除的冻结资金返回
                        await CompanyDao.unfreeze_company_funds(
                            query_db, company_uuid, apply_amount, return_to_balance=True
                        )
                        logger.info(f'已回滚资金操作 - 申请ID: {withdrawal_id}, 金额: {apply_amount}')
                    else:
                        logger.warning(f'无法回滚资金操作 - 申请ID: {withdrawal_id}, 当前冻结资金: {current_frozen}, 需要回滚金额: {apply_amount}')
                except Exception as fund_rollback_error:
                    logger.error(f'资金回滚检查失败 - 申请ID: {withdrawal_id}, 错误: {str(fund_rollback_error)}')

            if transactions_updated and withdrawal_transaction_records:
                # 回滚流水状态：将SUCCESS状态改回PENDING
                for transaction_record in withdrawal_transaction_records:
                    if transaction_record.business_type in ['WITHDRAWAL', 'TAX_DEDUCTION']:
                        await CompanyTransactionDao.update_transaction_status(
                            query_db, transaction_record.id, 'PENDING'
                        )
                logger.info(f'已回滚流水状态 - 申请ID: {withdrawal_id}, 流水数量: {len(withdrawal_transaction_records)}')

        except Exception as rollback_error:
            logger.error(f'回滚操作失败 - 申请ID: {withdrawal_id}, 回滚错误: {str(rollback_error)}', exc_info=True)

        # 回滚数据库事务
        await query_db.rollback()

        # 根据异常类型进行分类处理和日志记录
        if isinstance(exception, ServiceException):
            logger.error(f'处理提现申请业务错误 - 申请ID: {withdrawal_id}, 错误: {str(exception)}')
            raise exception
        elif isinstance(exception, ValueError):
            logger.error(f'处理提现申请参数错误 - 申请ID: {withdrawal_id}, 错误: {str(exception)}')
            raise ServiceException(message=f'参数错误: {str(exception)}')
        else:
            logger.error(
                f'处理提现申请系统错误 - 申请ID: {withdrawal_id}, '
                f'错误类型: {type(exception).__name__}, 错误: {str(exception)}',
                exc_info=True
            )
            raise ServiceException(message=f'系统错误，请联系管理员。错误类型: {type(exception).__name__}')

    @staticmethod
    def _get_status_name(status: str) -> str:
        """获取状态名称"""
        status_map = {
            'PENDING': '待审核',
            'APPROVED': '已通过',
            'REJECTED': '已拒绝',
            'PROCESSING': '处理中',
            'COMPLETED': '已完成',
            'CANCELLED': '已取消',
            'REJECTED_PROCESS': '处理驳回'
        }
        return status_map.get(status, status)

    @staticmethod
    def _get_withdrawal_type_name(withdrawal_type: int) -> str:
        """获取提现类型名称"""
        type_map = {
            1: '自行开票',
            2: '零工提现'
        }
        return type_map.get(withdrawal_type, '未知类型')

    @staticmethod
    def _get_transaction_type_name(transaction_type: int) -> str:
        """
        根据交易类型获取类型名称

        :param transaction_type: 交易类型
        :return: 交易类型名称
        """
        type_mapping = {
            1: '收入',
            2: '支出'
        }
        return type_mapping.get(transaction_type, '未知')
