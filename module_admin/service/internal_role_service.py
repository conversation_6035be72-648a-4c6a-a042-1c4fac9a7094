from datetime import datetime
from typing import List, Optional
from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.vo.internal_role_vo import InternalRoleModel, InternalRolePageQueryModel, DeleteInternalRoleModel
from module_admin.entity.do.sys_internal_role import SysInternalRole
from module_admin.entity.do.role_do import SysRoleMenu
from module_admin.entity.do.user_do import SysUserRole
from utils.common_util import CamelCaseUtil
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


class InternalRoleService:
    """内部角色管理服务"""

    @staticmethod
    async def get_role_list_services(
        query_db: AsyncSession,
        query_params: InternalRolePageQueryModel,
        data_scope_sql: str,
        is_page: bool = True
    ) -> PageResponseModel:
        """
        获取内部角色列表
        :param query_db: 数据库会话
        :param query_params: 查询参数
        :param data_scope_sql: 数据权限sql
        :param is_page: 是否分页
        :return: 内部角色列表
        """
        # 构建查询条件
        where_conditions = [
            SysInternalRole.del_flag == "0"
        ]
        if query_params.roleName:
            where_conditions.append(SysInternalRole.role_name.like(f"%{query_params.roleName}%"))
        if query_params.roleKey:
            where_conditions.append(SysInternalRole.role_key.like(f"%{query_params.roleKey}%"))
        if query_params.status:
            where_conditions.append(SysInternalRole.status == query_params.status)
        if query_params.beginTime and query_params.endTime:
            where_conditions.append(
                and_(
                    SysInternalRole.create_time >= datetime.strptime(query_params.beginTime, "%Y-%m-%d"),
                    SysInternalRole.create_time <= datetime.strptime(query_params.endTime, "%Y-%m-%d")
                )
            )

        # 构建查询语句
        select_sql = select(SysInternalRole).where(and_(*where_conditions))
        if is_page:
            # 获取总记录数
            count_sql = select(SysInternalRole).where(and_(*where_conditions))
            total = await query_db.scalar(count_sql)
            # 分页查询
            select_sql = select_sql.offset((query_params.pageNum - 1) * query_params.pageSize).limit(query_params.pageSize)

        # 执行查询
        result = await query_db.execute(select_sql)
        role_list = result.scalars().all()

        # 构建返回数据
        rows = []
        for role in role_list:
            role_dict = CamelCaseUtil.camel_case(role.__dict__)
            rows.append(role_dict)

        return PageResponseModel(
            total=total if is_page else len(rows),
            rows=rows
        )

    @staticmethod
    async def role_detail_services(query_db: AsyncSession, role_id: int) -> InternalRoleModel:
        """
        获取内部角色详细信息
        :param query_db: 数据库会话
        :param role_id: 角色ID
        :return: 内部角色信息
        """
        # 查询角色信息
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_id == role_id,
                    SysInternalRole.del_flag == "0"
                )
            )
        )
        role = result.scalar_one_or_none()
        if not role:
            return None

        # 查询角色菜单关联
        result = await query_db.execute(
            select(SysRoleMenu.menu_id).where(
                and_(
                    SysRoleMenu.role_id == role_id,
                    SysRoleMenu.role_type == "internal"
                )
            )
        )
        menu_ids = result.scalars().all()

        # 构建返回数据
        role_dict = CamelCaseUtil.camel_case(role.__dict__)
        role_dict["menuIds"] = menu_ids
        return InternalRoleModel(**role_dict)

    @staticmethod
    async def add_role_services(query_db: AsyncSession, add_role: InternalRoleModel) -> ResponseUtil:
        """
        新增内部角色
        :param query_db: 数据库会话
        :param add_role: 新增角色信息
        :return: 响应结果
        """
        # 检查角色名称是否唯一
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_name == add_role.roleName,
                    SysInternalRole.del_flag == "0"
                )
            )
        )
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg="新增角色'" + add_role.roleName + "'失败，角色名称已存在")

        # 检查权限字符是否唯一
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_key == add_role.roleKey,
                    SysInternalRole.del_flag == "0"
                )
            )
        )
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg="新增角色'" + add_role.roleName + "'失败，权限字符已存在")

        # 新增角色
        new_role = SysInternalRole(
            parent_id=add_role.parentId,
            role_name=add_role.roleName,
            role_key=add_role.roleKey,
            order_num=add_role.orderNum,
            status=add_role.status,
            create_by=add_role.createBy,
            create_time=add_role.createTime,
            update_by=add_role.updateBy,
            update_time=add_role.updateTime,
            remark=add_role.remark
        )
        query_db.add(new_role)
        await query_db.flush()
        role_id = new_role.role_id

        # 新增角色菜单关联
        if add_role.menuIds:
            for menu_id in add_role.menuIds:
                role_menu = SysRoleMenu(
                    role_id=role_id,
                    menu_id=menu_id,
                    role_type="internal"
                )
                query_db.add(role_menu)
        await query_db.commit()

        return ResponseUtil.success(msg="新增成功")

    @staticmethod
    async def edit_role_services(query_db: AsyncSession, edit_role: InternalRoleModel) -> ResponseUtil:
        """
        修改内部角色
        :param query_db: 数据库会话
        :param edit_role: 修改角色信息
        :return: 响应结果
        """
        # 检查角色是否存在
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_id == edit_role.roleId,
                    SysInternalRole.del_flag == "0"
                )
            )
        )
        role = result.scalar_one_or_none()
        if not role:
            return ResponseUtil.error(msg="修改角色'" + edit_role.roleName + "'失败，角色不存在")

        # 检查角色名称是否唯一
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_name == edit_role.roleName,
                    SysInternalRole.role_id != edit_role.roleId,
                    SysInternalRole.del_flag == "0"
                )
            )
        )
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg="修改角色'" + edit_role.roleName + "'失败，角色名称已存在")

        # 检查权限字符是否唯一
        result = await query_db.execute(
            select(SysInternalRole).where(
                and_(
                    SysInternalRole.role_key == edit_role.roleKey,
                    SysInternalRole.role_id != edit_role.roleId,
                    SysInternalRole.del_flag == "0"
                )
            )
        )
        if result.scalar_one_or_none():
            return ResponseUtil.error(msg="修改角色'" + edit_role.roleName + "'失败，权限字符已存在")

        # 修改角色
        role.parent_id = edit_role.parentId
        role.role_name = edit_role.roleName
        role.role_key = edit_role.roleKey
        role.order_num = edit_role.orderNum
        role.status = edit_role.status
        role.update_by = edit_role.updateBy
        role.update_time = edit_role.updateTime
        role.remark = edit_role.remark

        # 修改角色菜单关联
        await query_db.execute(
            SysRoleMenu.__table__.delete().where(
                and_(
                    SysRoleMenu.role_id == edit_role.roleId,
                    SysRoleMenu.role_type == "internal"
                )
            )
        )
        if edit_role.menuIds:
            for menu_id in edit_role.menuIds:
                role_menu = SysRoleMenu(
                    role_id=edit_role.roleId,
                    menu_id=menu_id,
                    role_type="internal"
                )
                query_db.add(role_menu)
        await query_db.commit()

        return ResponseUtil.success(msg="修改成功")

    @staticmethod
    async def delete_role_services(query_db: AsyncSession, delete_role: DeleteInternalRoleModel) -> ResponseUtil:
        """
        删除内部角色
        :param query_db: 数据库会话
        :param delete_role: 删除角色信息
        :return: 响应结果
        """
        role_ids = delete_role.roleIds.split(",")
        for role_id in role_ids:
            # 检查角色是否存在
            result = await query_db.execute(
                select(SysInternalRole).where(
                    and_(
                        SysInternalRole.role_id == role_id,
                        SysInternalRole.del_flag == "0"
                    )
                )
            )
            role = result.scalar_one_or_none()
            if not role:
                return ResponseUtil.error(msg="删除角色失败，角色不存在")

            # 检查是否存在子角色
            result = await query_db.execute(
                select(SysInternalRole).where(
                    and_(
                        SysInternalRole.parent_id == role_id,
                        SysInternalRole.del_flag == "0"
                    )
                )
            )
            if result.scalar_one_or_none():
                return ResponseUtil.error(msg="存在下级角色,不允许删除")

            # 检查是否存在用户使用该角色
            result = await query_db.execute(
                select(SysUserRole).where(
                    and_(
                        SysUserRole.role_id == role_id,
                        SysUserRole.role_type == "internal"
                    )
                )
            )
            if result.scalar_one_or_none():
                return ResponseUtil.error(msg="角色已分配,不能删除")

            # 删除角色
            role.del_flag = "1"
            role.update_by = delete_role.updateBy
            role.update_time = delete_role.updateTime

            # 删除角色菜单关联
            await query_db.execute(
                SysRoleMenu.__table__.delete().where(
                    and_(
                        SysRoleMenu.role_id == role_id,
                        SysRoleMenu.role_type == "internal"
                    )
                )
            )
        await query_db.commit()

        return ResponseUtil.success(msg="删除成功")

    @staticmethod
    async def check_role_allowed_services(role: InternalRoleModel) -> None:
        """
        校验角色是否允许操作
        :param role: 角色信息
        :return: None
        """
        if role.roleId == 1:
            raise ValueError("不允许操作超级管理员角色") 