from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.training_courses_dao import TrainingCoursesDao
from module_admin.dao.training_categories_dao import TrainingCategoriesDao
from module_admin.entity.vo.training_courses_vo import (
    TrainingCoursesModel, 
    TrainingCoursesPageQueryModel, 
    DeleteTrainingCoursesModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from exceptions.exception import ServiceException
from utils.common_util import CommonUtil


class TrainingCoursesService:
    """
    培训课程模块服务层
    """

    @classmethod
    async def get_training_courses_list_services(cls, query_db: AsyncSession, query_object: TrainingCoursesPageQueryModel, is_page: bool = False):
        """
        获取培训课程列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 培训课程列表信息对象
        """
        return await TrainingCoursesDao.get_training_courses_list(query_db, query_object, is_page)

    @classmethod
    def _add_content_type_name(cls, course):
        """
        添加内容类型名称

        :param course: 课程对象
        """
        content_type_map = {
            1: '视频',
            2: '文章',
            3: '图片',
            4: '音频'
        }
        if hasattr(course, 'content_type') and course.content_type:
            course.content_type_name = content_type_map.get(course.content_type, '未知')

    @classmethod
    def _add_content_type_name_to_dict(cls, course_dict):
        """
        为字典类型的课程数据添加内容类型名称

        :param course_dict: 课程字典
        """
        content_type_map = {
            1: '视频',
            2: '文章',
            3: '图片',
            4: '音频'
        }
        if 'content_type' in course_dict and course_dict['content_type']:
            course_dict['content_type_name'] = content_type_map.get(course_dict['content_type'], '未知')

    @classmethod
    def _get_content_type_name(cls, content_type):
        """
        根据内容类型获取类型名称

        :param content_type: 内容类型
        :return: 类型名称
        """
        content_type_map = {
            1: '视频',
            2: '文章',
            3: '图片',
            4: '音频'
        }
        return content_type_map.get(content_type, '未知')

    @classmethod
    async def training_courses_detail_services(cls, query_db: AsyncSession, course_id: str):
        """
        获取培训课程详细信息service

        :param query_db: orm对象
        :param course_id: 课程id
        :return: 培训课程信息对象
        """
        course = await TrainingCoursesDao.get_training_courses_detail_with_category_by_id(query_db, course_id)
        if course:
            # 增加浏览次数
            await TrainingCoursesDao.increment_views(query_db, course_id)
            await query_db.commit()
            return course
        else:
            raise ServiceException(message='培训课程不存在')

    @classmethod
    async def add_training_courses_services(cls, query_db: AsyncSession, add_course: TrainingCoursesModel):
        """
        新增培训课程信息service

        :param query_db: orm对象
        :param add_course: 新增课程对象
        :return: 新增课程校验结果
        """
        # 检查课程标题是否已存在
        existing_course = await TrainingCoursesDao.get_training_courses_detail_by_title(query_db, add_course.title)
        if existing_course:
            raise ServiceException(message='课程标题已存在')
        
        # 检查分类是否存在
        if add_course.category_id:
            category = await TrainingCategoriesDao.get_training_categories_detail_by_id(query_db, add_course.category_id)
            if not category:
                raise ServiceException(message='所选分类不存在')
        
        # 生成课程ID和UUID
        if not add_course.id:
            add_course.id = await TrainingCoursesDao.get_next_course_id(query_db)
        if not add_course.uuid:
            add_course.uuid = await TrainingCoursesDao.get_next_course_uuid(query_db)
        
        try:
            await TrainingCoursesDao.add_training_courses_dao(query_db, add_course.model_dump(exclude_unset=True))
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'新增失败: {str(e)}')

    @classmethod
    async def edit_training_courses_services(cls, query_db: AsyncSession, edit_course: TrainingCoursesModel):
        """
        编辑培训课程信息service

        :param query_db: orm对象
        :param edit_course: 编辑课程对象
        :return: 编辑课程校验结果
        """
        # 检查课程是否存在
        existing_course = await TrainingCoursesDao.get_training_courses_detail_by_id(query_db, edit_course.id)
        if not existing_course:
            raise ServiceException(message='培训课程不存在')
        
        # 检查课程标题是否与其他课程重复
        title_check_course = await TrainingCoursesDao.get_training_courses_detail_by_title(
            query_db, edit_course.title, edit_course.id
        )
        if title_check_course:
            raise ServiceException(message='课程标题已存在')
        
        # 检查分类是否存在
        if edit_course.category_id:
            category = await TrainingCategoriesDao.get_training_categories_detail_by_id(query_db, edit_course.category_id)
            if not category:
                raise ServiceException(message='所选分类不存在')
        
        # 更新字段
        for key, value in edit_course.model_dump(exclude_unset=True, exclude={'id'}).items():
            if hasattr(existing_course, key) and value is not None:
                setattr(existing_course, key, value)
        
        try:
            await TrainingCoursesDao.edit_training_courses_dao(query_db, existing_course)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='编辑成功')
        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'编辑失败: {str(e)}')

    @classmethod
    async def delete_training_courses_services(cls, query_db: AsyncSession, delete_course: DeleteTrainingCoursesModel):
        """
        删除培训课程信息service

        :param query_db: orm对象
        :param delete_course: 删除课程对象
        :return: 删除课程校验结果
        """
        if delete_course.ids:
            try:
                await TrainingCoursesDao.delete_training_courses_dao(query_db, delete_course)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise ServiceException(message=f'删除失败: {str(e)}')
        else:
            raise ServiceException(message='传入课程id为空')
