import io
from openpyxl import Workbook
from openpyxl.styles import <PERSON><PERSON><PERSON>, <PERSON><PERSON>Fill, Font
from openpyxl.utils import get_column_letter
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.company_transaction_dao import CompanyTransactionDao
from module_admin.dao.company_dao import CompanyDao
from module_admin.entity.vo.company_transaction_vo import (
    CompanyTransactionPageQueryModel,
    CompanyTransactionQueryModel,
    CompanyTransactionResponseModel
)
from utils.common_util import CamelCaseUtil
from utils.excel_util import ExcelUtil


class CompanyTransactionService:
    """
    公司资金流水服务层
    """

    @classmethod
    async def get_company_transaction_list_services(
        cls,
        query_db: AsyncSession,
        query_object: CompanyTransactionPageQueryModel,
        is_page: bool = False
    ):
        """
        获取公司资金流水列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司资金流水列表信息对象
        """
        transaction_list_result = await CompanyTransactionDao.get_company_transaction_list(
            query_db, query_object, is_page
        )

        # 获取所有涉及的公司UUID，用于批量获取支付类型映射
        company_uuids = set()

        if is_page:
            # 分页结果处理 - PageUtil已经转换为字典格式
            if transaction_list_result.rows:
                for item_dict in transaction_list_result.rows:
                    company_uuids.add(item_dict.get('companyUuid'))
        else:
            # 非分页结果处理 - 需要手动转换格式
            if transaction_list_result:
                for item in transaction_list_result:
                    company_uuids.add(item.company_uuid)

        # 批量获取所有公司的支付类型映射
        pay_type_mappings = {}
        for company_uuid in company_uuids:
            if company_uuid:
                mapping = await CompanyDao.get_pay_types_mapping(query_db, company_uuid)
                pay_type_mappings[company_uuid] = mapping

        if is_page:
            # 分页结果处理 - PageUtil已经转换为字典格式
            if transaction_list_result.rows:
                # 添加交易类型名称和支付类型名称
                for item_dict in transaction_list_result.rows:
                    item_dict['transactionTypeName'] = cls._get_transaction_type_name(item_dict.get('transactionType', 1))
                    # 添加支付类型名称
                    company_uuid = item_dict.get('companyUuid')
                    pay_type_code = item_dict.get('payType')
                    if company_uuid and pay_type_code and company_uuid in pay_type_mappings:
                        item_dict['payTypeName'] = pay_type_mappings[company_uuid].get(pay_type_code, pay_type_code)
                    else:
                        item_dict['payTypeName'] = pay_type_code or '无'
        else:
            # 非分页结果处理 - 需要手动转换格式
            if transaction_list_result:
                converted_data = []
                for item in transaction_list_result:
                    item_dict = CamelCaseUtil.transform_result([item])[0]
                    # 添加交易类型名称
                    item_dict['transactionTypeName'] = cls._get_transaction_type_name(item_dict.get('transactionType', 1))
                    # 添加支付类型名称
                    company_uuid = item_dict.get('companyUuid')
                    pay_type_code = item_dict.get('payType')
                    if company_uuid and pay_type_code and company_uuid in pay_type_mappings:
                        item_dict['payTypeName'] = pay_type_mappings[company_uuid].get(pay_type_code, pay_type_code)
                    else:
                        item_dict['payTypeName'] = pay_type_code or '无'
                    converted_data.append(item_dict)
                transaction_list_result = converted_data

        return transaction_list_result

    @classmethod
    async def get_company_transaction_detail_services(cls, query_db: AsyncSession, transaction_id: int):
        """
        获取公司资金流水详情信息service

        :param query_db: orm对象
        :param transaction_id: 流水ID
        :return: 公司资金流水详情信息对象
        """
        transaction_detail = await CompanyTransactionDao.get_company_transaction_detail_by_id(
            query_db, transaction_id
        )

        if transaction_detail:
            # 转换数据格式
            detail_dict = CamelCaseUtil.transform_result([transaction_detail])[0]
            # 添加交易类型名称
            detail_dict['transactionTypeName'] = cls._get_transaction_type_name(detail_dict.get('transactionType', 1))

            # 添加支付类型名称
            company_uuid = detail_dict.get('companyUuid')
            pay_type_code = detail_dict.get('payType')
            if company_uuid and pay_type_code:
                pay_type_mapping = await CompanyDao.get_pay_types_mapping(query_db, company_uuid)
                detail_dict['payTypeName'] = pay_type_mapping.get(pay_type_code, pay_type_code)
            else:
                detail_dict['payTypeName'] = pay_type_code or '无'

            return detail_dict

        return None

    @classmethod
    async def get_company_transactions_by_ids_services(cls, query_db: AsyncSession, transaction_ids: list):
        """
        根据流水ID列表批量获取资金流水信息service

        :param query_db: orm对象
        :param transaction_ids: 流水ID列表
        :return: 资金流水信息列表
        """
        if not transaction_ids:
            return []

        # 获取资金流水记录
        transactions = await CompanyTransactionDao.get_company_transactions_by_ids(query_db, transaction_ids)

        if not transactions:
            return []

        # 转换数据格式
        transaction_list = CamelCaseUtil.transform_result(transactions)

        # 获取所有涉及的公司UUID，用于批量获取支付类型映射
        company_uuids = set()
        for transaction_dict in transaction_list:
            company_uuids.add(transaction_dict.get('companyUuid'))

        # 批量获取支付类型映射
        pay_type_mappings = {}
        for company_uuid in company_uuids:
            if company_uuid:
                pay_type_mapping = await CompanyDao.get_pay_types_mapping(query_db, company_uuid)
                pay_type_mappings[company_uuid] = pay_type_mapping

        # 处理每条记录
        for transaction_dict in transaction_list:
            # 添加交易类型名称
            transaction_dict['transactionTypeName'] = cls._get_transaction_type_name(
                transaction_dict.get('transactionType', 1)
            )

            # 添加支付类型名称
            company_uuid = transaction_dict.get('companyUuid')
            pay_type_code = transaction_dict.get('payType')
            if company_uuid and pay_type_code and company_uuid in pay_type_mappings:
                transaction_dict['payTypeName'] = pay_type_mappings[company_uuid].get(pay_type_code, pay_type_code)
            else:
                transaction_dict['payTypeName'] = pay_type_code or '无'

        return transaction_list

    @classmethod
    async def export_company_transaction_list_services(cls, transaction_list_result):
        """
        导出公司资金流水列表信息service

        :param transaction_list_result: 公司资金流水列表数据
        :return: 导出结果
        """
        # 处理导出数据，确保包含所有详情字段
        processed_data = []
        for item in transaction_list_result:
            # 处理数据，确保所有字段都有值，空值显示为"无"
            processed_item = {
                'transactionNo': item.get('transactionNo', ''),
                'companyName': item.get('companyName', '未知公司'),
                'businessTypeName': item.get('businessTypeName', ''),
                'transactionTypeName': item.get('transactionTypeName', ''),
                'amount': f"{'+' if item.get('transactionType') == 1 else '-'}{item.get('amount', 0)}",
                'balanceBefore': item.get('balanceBefore', 0),
                'balanceAfter': item.get('balanceAfter', 0),
                'relatedOrderNo': item.get('relatedOrderNo') or '无',
                'payTypeName': item.get('payTypeName') or '无',  # 使用转换后的支付类型名称
                'externalTransactionId': item.get('externalTransactionId') or '无',
                'operatorName': item.get('operatorName', ''),
                'transactionTime': item.get('transactionTime', ''),
                'description': item.get('description', ''),
                'remark': item.get('remark') or '无'
            }
            processed_data.append(processed_item)

        # 定义完整的导出字段映射（按详情页面顺序）
        export_mapping = {
            'transactionNo': '流水号',
            'companyName': '公司名称',
            'businessTypeName': '业务类型',
            'transactionTypeName': '交易类型',
            'amount': '交易金额',
            'balanceBefore': '交易前余额',
            'balanceAfter': '交易后余额',
            'relatedOrderNo': '关联订单号',
            'payTypeName': '支付方式',  # 使用转换后的支付类型名称字段
            'externalTransactionId': '外部交易ID',
            'operatorName': '操作人',
            'transactionTime': '交易时间',
            'description': '交易描述',
            'remark': '备注'
        }

        # 使用自定义方法创建Excel，确保列宽适合内容
        export_result = cls._create_excel_with_auto_width(processed_data, export_mapping)
        return export_result

    @classmethod
    def _create_excel_with_auto_width(cls, data_list, mapping_dict):
        """
        创建Excel文件，自动调整列宽以适合内容

        :param data_list: 数据列表
        :param mapping_dict: 字段映射字典
        :return: Excel文件的二进制数据
        """
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "公司资金流水"

        # 设置表头样式
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        header_font = Font(color='FFFFFF', bold=True)
        header_alignment = Alignment(horizontal='center', vertical='center')

        # 写入表头
        headers = list(mapping_dict.values())
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = header_alignment

        # 写入数据
        for row_num, item in enumerate(data_list, 2):
            for col_num, field_key in enumerate(mapping_dict.keys(), 1):
                value = item.get(field_key, '')
                cell = ws.cell(row=row_num, column=col_num, value=value)
                cell.alignment = Alignment(horizontal='left', vertical='center')

        # 自动调整列宽
        for col_num, field_key in enumerate(mapping_dict.keys(), 1):
            column_letter = get_column_letter(col_num)
            header_length = len(mapping_dict[field_key])

            # 计算该列的最大内容长度
            max_length = header_length
            for item in data_list:
                value = str(item.get(field_key, ''))
                # 中文字符按2个字符计算宽度
                content_length = sum(2 if ord(char) > 127 else 1 for char in value)
                max_length = max(max_length, content_length)

            # 设置列宽，最小12，最大50
            column_width = min(max(max_length + 2, 12), 50)
            ws.column_dimensions[column_letter].width = column_width

        # 设置行高
        for row in ws.iter_rows():
            ws.row_dimensions[row[0].row].height = 20

        # 保存为二进制数据
        binary_data = io.BytesIO()
        wb.save(binary_data)
        binary_data.seek(0)

        return binary_data.getvalue()

    @classmethod
    def _get_transaction_type_name(cls, transaction_type: int) -> str:
        """
        根据交易类型获取类型名称

        :param transaction_type: 交易类型
        :return: 交易类型名称
        """
        type_mapping = {
            1: '收入',
            2: '支出'
        }
        return type_mapping.get(transaction_type, '未知')
