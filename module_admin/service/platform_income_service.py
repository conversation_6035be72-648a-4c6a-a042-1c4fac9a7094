import io
from sqlalchemy.ext.asyncio import AsyncSession
from openpyxl import Workbook
from openpyxl.styles import Alignment, <PERSON><PERSON>Fill, Font
from openpyxl.utils import get_column_letter
from module_admin.dao.company_transaction_dao import CompanyTransactionDao
from module_admin.entity.vo.company_transaction_vo import CompanyTransactionPageQueryModel, CompanyTransactionStatisticsQueryModel, CompanyTransactionStatisticsQueryModel
from utils.common_util import CamelCaseUtil


class PlatformIncomeService:
    """
    平台收入管理模块服务层
    """

    @classmethod
    async def get_platform_income_list_services(
        cls, query_db: AsyncSession, query_object: CompanyTransactionPageQueryModel, is_page: bool = False
    ):
        """
        获取平台收入流水列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 平台收入流水列表信息对象
        """
        # 查询条件已在controller层设定
        
        transaction_list_result = await CompanyTransactionDao.get_company_transaction_list(
            query_db, query_object, is_page
        )

        # 转换数据格式
        if is_page:
            # 分页结果是PageResponseModel对象，需要访问其属性
            result_list = CamelCaseUtil.transform_result(transaction_list_result.rows)
            # 添加交易类型名称
            for item in result_list:
                item['transactionTypeName'] = cls._get_transaction_type_name(item.get('transactionType', 1))
            transaction_list_result.rows = result_list
        else:
            result_list = CamelCaseUtil.transform_result(transaction_list_result)
            # 添加交易类型名称
            for item in result_list:
                item['transactionTypeName'] = cls._get_transaction_type_name(item.get('transactionType', 1))
            transaction_list_result = result_list

        return transaction_list_result

    @classmethod
    async def get_platform_income_detail_services(cls, query_db: AsyncSession, transaction_id: int):
        """
        获取平台收入流水详情信息service

        :param query_db: orm对象
        :param transaction_id: 流水ID
        :return: 平台收入流水详情信息对象
        """
        transaction_detail = await CompanyTransactionDao.get_company_transaction_detail_by_id(
            query_db, transaction_id
        )

        if transaction_detail and transaction_detail.business_type not in ['SOFTWARE_RENEWAL', 'RECHARGE']:
            # 转换数据格式
            detail_dict = CamelCaseUtil.transform_result([transaction_detail])[0]
            # 添加交易类型名称
            detail_dict['transactionTypeName'] = cls._get_transaction_type_name(detail_dict.get('transactionType', 1))

            return detail_dict

        return None

    @classmethod
    async def get_platform_income_statistics_services(cls, query_db: AsyncSession, query_object: 'CompanyTransactionQueryModel' = None):
        """
        获取平台收入统计数据service

        :param query_db: orm对象
        :param query_object: 查询参数对象（可选）
        :return: 平台收入统计数据
        """
        # 创建统计查询对象
        statistics_query = CompanyTransactionStatisticsQueryModel(
            exclude_business_types='SOFTWARE_RENEWAL,RECHARGE,WITHDRAWAL,TAX_DEDUCTION',  # 排除软件续费、余额充值、提现申请和代扣个税
            transaction_type=2,  # 只统计支出类型（相对于平台是收入）
            transaction_status='SUCCESS'  # 只统计成功的交易
        )

        # 如果有查询参数，添加筛选条件
        if query_object:
            if query_object.company_uuid:
                statistics_query.company_uuid = query_object.company_uuid
            if query_object.transaction_no:
                statistics_query.transaction_no = query_object.transaction_no
            if query_object.operator_name:
                statistics_query.operator_name = query_object.operator_name
            if query_object.transaction_status:
                statistics_query.transaction_status = query_object.transaction_status
            if query_object.begin_time:
                statistics_query.begin_time = query_object.begin_time
            if query_object.end_time:
                statistics_query.end_time = query_object.end_time
            # 如果查询参数中有exclude_business_types，使用查询参数的值
            if hasattr(query_object, 'exclude_business_types') and query_object.exclude_business_types:
                statistics_query.exclude_business_types = query_object.exclude_business_types

        # 获取统计数据
        statistics_result = await CompanyTransactionDao.get_company_transaction_statistics(
            query_db, statistics_query
        )

        return {
            'totalAmount': statistics_result.get('total_amount', 0)
        }

    @classmethod
    async def export_platform_income_list_services(cls, platform_income_list_result):
        """
        导出平台收入流水列表信息service

        :param platform_income_list_result: 平台收入流水列表信息对象
        :return: 平台收入流水列表导出信息对象
        """
        # 处理数据，确保格式一致
        processed_data = []
        if hasattr(platform_income_list_result, 'rows'):
            # 分页数据格式 (PageResponseModel)
            data_source = platform_income_list_result.rows
        elif isinstance(platform_income_list_result, dict) and 'rows' in platform_income_list_result:
            # 字典格式的分页数据
            data_source = platform_income_list_result['rows']
        else:
            # 列表数据格式
            data_source = platform_income_list_result

        for item in data_source:
            processed_item = {
                'transactionNo': item.get('transactionNo', ''),
                'companyUuid': item.get('companyUuid', ''),
                'businessTypeName': item.get('businessTypeName', ''),
                'transactionTypeName': item.get('transactionTypeName', ''),
                'amount': item.get('amount', ''),
                'balanceBefore': item.get('balanceBefore', ''),
                'balanceAfter': item.get('balanceAfter', ''),
                'relatedOrderNo': item.get('relatedOrderNo', ''),
                'payType': item.get('payType', ''),
                'externalTransactionId': item.get('externalTransactionId', ''),
                'operatorName': item.get('operatorName', ''),
                'transactionStatus': cls._get_status_text(item.get('transactionStatus', '')),
                'description': item.get('description', ''),
                'transactionTime': item.get('transactionTime', ''),
                'remark': item.get('remark', '')
            }
            processed_data.append(processed_item)

        # 定义字段映射
        export_mapping = {
            'transactionNo': '流水号',
            'companyUuid': '公司UUID',
            'businessTypeName': '业务类型',
            'transactionTypeName': '交易类型',
            'amount': '交易金额',
            'balanceBefore': '交易前余额',
            'balanceAfter': '交易后余额',
            'relatedOrderNo': '关联订单号',
            'payType': '支付方式',
            'externalTransactionId': '外部交易ID',
            'operatorName': '操作人',
            'transactionStatus': '交易状态',
            'description': '交易描述',
            'transactionTime': '交易时间',
            'remark': '备注'
        }

        # 使用自定义方法创建Excel
        export_result = cls._create_excel_with_auto_width(processed_data, export_mapping, '平台收入流水')
        return export_result

    @classmethod
    def _create_excel_with_auto_width(cls, data_list, mapping_dict, sheet_name):
        """
        创建Excel文件，自动调整列宽以适合内容

        :param data_list: 数据列表
        :param mapping_dict: 字段映射字典
        :param sheet_name: 工作表名称
        :return: Excel文件的二进制数据
        """
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = sheet_name

        # 设置表头样式
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        header_font = Font(color='FFFFFF', bold=True)
        header_alignment = Alignment(horizontal='center', vertical='center')

        # 写入表头
        headers = list(mapping_dict.values())
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = header_alignment

        # 写入数据
        for row_num, item in enumerate(data_list, 2):
            for col_num, field_key in enumerate(mapping_dict.keys(), 1):
                value = item.get(field_key, '')
                cell = ws.cell(row=row_num, column=col_num, value=value)
                cell.alignment = Alignment(horizontal='left', vertical='center')

        # 自动调整列宽
        for col_num, field_key in enumerate(mapping_dict.keys(), 1):
            column_letter = get_column_letter(col_num)
            header_length = len(mapping_dict[field_key])

            # 计算该列的最大内容长度
            max_length = header_length
            for item in data_list:
                value = str(item.get(field_key, ''))
                # 中文字符按2个字符计算宽度
                content_length = sum(2 if ord(char) > 127 else 1 for char in value)
                max_length = max(max_length, content_length)

            # 设置列宽，最小12，最大50
            column_width = min(max(max_length + 2, 12), 50)
            ws.column_dimensions[column_letter].width = column_width

        # 设置行高
        for row in ws.iter_rows():
            ws.row_dimensions[row[0].row].height = 20

        # 保存为二进制数据
        binary_data = io.BytesIO()
        wb.save(binary_data)
        binary_data.seek(0)

        return binary_data.getvalue()

    @classmethod
    def _get_transaction_type_name(cls, transaction_type: int) -> str:
        """
        根据交易类型获取类型名称

        :param transaction_type: 交易类型
        :return: 交易类型名称
        """
        type_mapping = {
            1: '收入',
            2: '支出'
        }
        return type_mapping.get(transaction_type, '未知')

    @classmethod
    def _get_status_text(cls, status: str) -> str:
        """
        根据状态获取状态文本

        :param status: 状态
        :return: 状态文本
        """
        status_mapping = {
            'SUCCESS': '成功',
            'PENDING': '待处理',
            'FAILED': '失败',
            'CANCELLED': '已取消'
        }
        return status_mapping.get(status, status)
