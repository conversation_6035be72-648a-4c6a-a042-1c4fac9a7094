from datetime import datetime
from decimal import Decimal
from typing import Optional, Literal, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class CompanyWithdrawalModel(BaseModel):
    """
    公司提现申请模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='提现申请ID')
    company_uuid: Optional[str] = Field(default=None, description='公司UUID')
    withdrawal_no: Optional[str] = Field(default=None, description='提现申请单号')
    withdrawal_type: Optional[int] = Field(default=None, description='提现类型：1-自行开票，2-零工提现')
    withdrawal_type_name: Optional[str] = Field(default=None, description='提现类型名称')
    apply_amount: Optional[Decimal] = Field(default=None, description='申请提现金额')
    fee_rate: Optional[Decimal] = Field(default=None, description='手续费率')
    fee_amount: Optional[Decimal] = Field(default=None, description='手续费金额')
    actual_amount: Optional[Decimal] = Field(default=None, description='实际到账金额')
    bank_name: Optional[str] = Field(default=None, description='开户银行')
    bank_account: Optional[str] = Field(default=None, description='银行账号')
    account_holder: Optional[str] = Field(default=None, description='开户人姓名')
    invoice_info: Optional[str] = Field(default=None, description='开票信息（JSON格式）')
    apply_reason: Optional[str] = Field(default=None, description='申请原因')
    status: Optional[Literal['PENDING', 'APPROVED', 'REJECTED', 'PROCESSING', 'COMPLETED', 'CANCELLED']] = Field(default=None, description='申请状态')
    applicant_id: Optional[str] = Field(default=None, description='申请人ID')
    applicant_name: Optional[str] = Field(default=None, description='申请人姓名')
    apply_time: Optional[datetime] = Field(default=None, description='申请时间')
    reviewer_id: Optional[str] = Field(default=None, description='审核人ID')
    reviewer_name: Optional[str] = Field(default=None, description='审核人姓名')
    review_time: Optional[datetime] = Field(default=None, description='审核时间')
    review_comment: Optional[str] = Field(default=None, description='审核意见')
    processor_id: Optional[str] = Field(default=None, description='处理人ID')
    processor_name: Optional[str] = Field(default=None, description='处理人姓名')
    process_time: Optional[datetime] = Field(default=None, description='处理时间')
    completion_time: Optional[datetime] = Field(default=None, description='完成时间')
    transaction_id: Optional[str] = Field(default=None, description='交易流水号')
    remark: Optional[str] = Field(default=None, description='备注')
    created_by: Optional[str] = Field(default=None, description='创建人')
    created_at: Optional[datetime] = Field(default=None, description='创建时间')
    updated_by: Optional[str] = Field(default=None, description='更新人')
    updated_at: Optional[datetime] = Field(default=None, description='更新时间')


@as_query
class CompanyWithdrawalPageQueryModel(CompanyWithdrawalModel):
    """
    公司提现申请分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页条数')
    reviewer_name: Optional[str] = Field(default=None, description='审核人姓名')
    processor_name: Optional[str] = Field(default=None, description='处理人姓名')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
    ids: Optional[str] = Field(default=None, description='指定导出的ID列表，逗号分隔')
    export_data: Optional[str] = Field(default=None, description='前端处理好的导出数据JSON字符串')


@as_query  
class CompanyWithdrawalQueryModel(CompanyWithdrawalModel):
    """
    公司提现申请查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


class CompanyWithdrawalCreateModel(BaseModel):
    """
    创建提现申请模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    company_uuid: str = Field(..., description='公司UUID')
    withdrawal_type: int = Field(..., description='提现类型：1-自行开票，2-零工提现')
    apply_amount: Decimal = Field(..., gt=0, description='申请提现金额，必须大于0')
    source_transaction_ids: str = Field(..., description='关联的资金流水记录ID，多个用逗号分隔')
    bank_name: Optional[str] = Field(default=None, description='开户银行')
    bank_account: Optional[str] = Field(default=None, description='银行账号')
    account_holder: Optional[str] = Field(default=None, description='开户人姓名')
    invoice_info: Optional[Dict[str, Any]] = Field(default=None, description='开票信息')
    apply_reason: Optional[str] = Field(default=None, description='申请原因')
    remark: Optional[str] = Field(default=None, description='备注')


class CompanyWithdrawalReviewModel(BaseModel):
    """
    审核提现申请模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    action: Literal['approve', 'reject'] = Field(..., description='审核动作：approve-通过，reject-拒绝')
    review_comment: Optional[str] = Field(default=None, description='审核意见')


class CompanyWithdrawalProcessModel(BaseModel):
    """
    处理提现申请模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    action: Literal['complete', 'reject'] = Field(..., description='处理动作：complete-完成，reject-驳回')
    transaction_id: Optional[str] = Field(default=None, description='交易流水号')
    remark: Optional[str] = Field(default=None, description='处理备注')


class CompanyWithdrawalResponseModel(BaseModel):
    """
    公司提现申请响应模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: int = Field(description='提现申请ID')
    company_uuid: str = Field(description='公司UUID')
    withdrawal_no: str = Field(description='提现申请单号')
    withdrawal_type: int = Field(description='提现类型：1-自行开票，2-零工提现')
    withdrawal_type_name: str = Field(description='提现类型名称')
    apply_amount: Decimal = Field(description='申请提现金额')
    fee_rate: Decimal = Field(description='手续费率')
    fee_amount: Decimal = Field(description='手续费金额')
    actual_amount: Decimal = Field(description='实际到账金额')
    bank_name: Optional[str] = Field(default=None, description='开户银行')
    bank_account: Optional[str] = Field(default=None, description='银行账号')
    account_holder: Optional[str] = Field(default=None, description='开户人姓名')
    invoice_info: Optional[str] = Field(default=None, description='开票信息（JSON格式）')
    apply_reason: Optional[str] = Field(default=None, description='申请原因')
    source_transaction_ids: Optional[str] = Field(default=None, description='关联的资金流水记录ID，多个用逗号分隔')
    status: str = Field(description='申请状态')
    status_name: str = Field(description='申请状态名称')
    applicant_id: str = Field(description='申请人ID')
    applicant_name: str = Field(description='申请人姓名')
    apply_time: datetime = Field(description='申请时间')
    reviewer_id: Optional[str] = Field(default=None, description='审核人ID')
    reviewer_name: Optional[str] = Field(default=None, description='审核人姓名')
    review_time: Optional[datetime] = Field(default=None, description='审核时间')
    review_comment: Optional[str] = Field(default=None, description='审核意见')
    processor_id: Optional[str] = Field(default=None, description='处理人ID')
    processor_name: Optional[str] = Field(default=None, description='处理人姓名')
    process_time: Optional[datetime] = Field(default=None, description='处理时间')
    completion_time: Optional[datetime] = Field(default=None, description='完成时间')
    transaction_id: Optional[str] = Field(default=None, description='交易流水号')
    remark: Optional[str] = Field(default=None, description='备注')
    created_by: str = Field(description='创建人')
    created_at: datetime = Field(description='创建时间')
    updated_by: Optional[str] = Field(default=None, description='更新人')
    updated_at: Optional[datetime] = Field(default=None, description='更新时间')
