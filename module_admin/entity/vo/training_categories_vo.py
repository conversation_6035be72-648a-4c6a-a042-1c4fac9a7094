from datetime import datetime
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class TrainingCategoriesModel(BaseModel):
    """
    培训分类表对应的模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[str] = Field(default=None, description='分类ID')
    uuid: Optional[str] = Field(default=None, description='分类唯一标识')
    name: Optional[str] = Field(default=None, description='分类名称')
    description: Optional[str] = Field(default=None, description='分类描述')
    icon: Optional[str] = Field(default=None, description='分类图标')
    parent_id: Optional[str] = Field(default=None, description='父分类ID')
    sort_order: Optional[int] = Field(default=0, description='排序权重')
    status: Optional[int] = Field(default=1, description='状态(1:启用,0:禁用)')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class TrainingCategoriesQueryModel(BaseModel):
    """
    培训分类查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    name: Optional[str] = Field(default=None, description='分类名称')
    status: Optional[int] = Field(default=None, description='状态')
    parent_id: Optional[str] = Field(default=None, description='父分类ID')


@as_query
class TrainingCategoriesPageQueryModel(TrainingCategoriesQueryModel):
    """
    培训分类分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')





class DeleteTrainingCategoriesModel(BaseModel):
    """
    删除培训分类模型
    """
    ids: str = Field(description='需要删除的分类ID，多个用逗号分隔')


class TrainingCategoriesTreeModel(BaseModel):
    """
    培训分类树形结构模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: str = Field(description='分类ID')
    uuid: str = Field(description='分类唯一标识')
    name: str = Field(description='分类名称')
    description: Optional[str] = Field(default=None, description='分类描述')
    icon: Optional[str] = Field(default=None, description='分类图标')
    parent_id: Optional[str] = Field(default=None, description='父分类ID')
    sort_order: int = Field(default=0, description='排序权重')
    status: int = Field(default=1, description='状态(1:启用,0:禁用)')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    children: Optional[List['TrainingCategoriesTreeModel']] = Field(default=None, description='子分类列表')
