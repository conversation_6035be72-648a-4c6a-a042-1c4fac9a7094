from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field


class InternalMenuModel(BaseModel):
    """内部菜单模型"""
    menuId: Optional[int] = Field(None, description="菜单ID")
    parentId: Optional[int] = Field(0, description="父菜单ID")
    menuName: str = Field(..., description="菜单名称")
    menuType: str = Field(..., description="菜单类型: M目录 C菜单 F按钮")
    clientType: Optional[int] = Field(0, description="客户端类型: 0 sys网页端 1 store门店端")
    icon: Optional[str] = Field(None, description="菜单图标")
    orderNum: Optional[int] = Field(0, description="显示顺序")
    path: Optional[str] = Field("", description="路由地址")
    component: Optional[str] = Field(None, description="组件路径")
    isNew: Optional[int] = Field(None, description="是否新上菜单")
    isCache: Optional[int] = Field(0, description="是否缓存: 0缓存 1不缓存")
    visible: Optional[str] = Field("0", description="菜单状态: 0显示 1隐藏")
    status: Optional[str] = Field("0", description="菜单状态: 0正常 1停用")
    perms: Optional[str] = Field(None, description="权限标识")
    createTime: Optional[datetime] = Field(None, description="创建时间")
    updateTime: Optional[datetime] = Field(None, description="更新时间")
    remark: Optional[str] = Field("", description="备注")
    
    class Config:
        orm_mode = True


class InternalMenuQueryModel(BaseModel):
    """内部菜单查询模型"""
    menuName: Optional[str] = Field(None, description="菜单名称")
    visible: Optional[str] = Field(None, description="是否显示: 0显示 1隐藏")
    status: Optional[str] = Field(None, description="状态: 0正常 1停用")
    clientType: Optional[int] = Field(None, description="客户端类型: 0 sys网页端 1 store门店端")
    
    @classmethod
    def as_query(cls, menuName: Optional[str] = None, visible: Optional[str] = None, 
                status: Optional[str] = None, clientType: Optional[int] = None):
        return cls(menuName=menuName, visible=visible, status=status, clientType=clientType)


class InternalMenuPageQueryModel(BaseModel):
    """内部菜单分页查询模型"""
    pageNum: int = Field(1, description="页码")
    pageSize: int = Field(10, description="每页数量")
    menuName: Optional[str] = Field(None, description="菜单名称")
    visible: Optional[str] = Field(None, description="是否显示")
    status: Optional[str] = Field(None, description="状态")
    clientType: Optional[int] = Field(None, description="客户端类型: 0 sys网页端 1 store门店端")
    beginTime: Optional[str] = Field(None, description="开始时间")
    endTime: Optional[str] = Field(None, description="结束时间")


class DeleteInternalMenuModel(BaseModel):
    """删除内部菜单模型"""
    menuIds: str = Field(..., description="菜单ID，多个以逗号分隔")


class InternalMenuTreeSelectModel(BaseModel):
    """内部菜单树选择模型"""
    id: int = Field(..., description="节点ID")
    label: str = Field(..., description="节点名称")
    children: Optional[List['InternalMenuTreeSelectModel']] = Field(None, description="子节点")


class InternalRoleMenuTreeSelectModel(BaseModel):
    """内部角色菜单树选择模型"""
    menus: List[InternalMenuTreeSelectModel] = Field(..., description="菜单树")
    checkedKeys: List[int] = Field(..., description="选中节点列表")


InternalMenuTreeSelectModel.update_forward_refs() 