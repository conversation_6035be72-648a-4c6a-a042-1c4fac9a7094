from datetime import datetime
from decimal import Decimal
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class ProductSyncModel(BaseModel):
    """
    产品同步管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='产品ID')
    product_name: Optional[str] = Field(default=None, description='产品昵称')
    company_uuid: Optional[str] = Field(default=None, description='公司UUID')
    company_id: Optional[str] = Field(default=None, description='门店ID')
    company_name: Optional[str] = Field(default=None, description='公司昵称')
    service_skill_name: Optional[str] = Field(default=None, description='产品类型')
    service_skill_id: Optional[str] = Field(default=None, description='服务技能ID')
    img_id: Optional[str] = Field(default=None, description='图片ID')
    serve_type_name: Optional[str] = Field(default=None, description='服务类型名称')
    online_store_num: Optional[int] = Field(default=None, description='在线门店数量')
    sum_num: Optional[int] = Field(default=None, description='总数量')
    type_name: Optional[str] = Field(default=None, description='类型名称')
    product_status: Optional[int] = Field(default=None, description='产品状态')
    is_delete: Optional[int] = Field(default=None, description='是否删除')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class ProductSyncQueryModel(BaseModel):
    """
    产品同步查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    product_name: Optional[str] = Field(default=None, description='产品昵称')
    company_name: Optional[str] = Field(default=None, description='公司昵称')
    service_skill_name: Optional[str] = Field(default=None, description='产品类型')
    product_status: Optional[int] = Field(default=None, description='产品状态')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class ProductSyncPageQueryModel(ProductSyncQueryModel):
    """
    产品同步分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页条数')


class DeleteProductSyncModel(BaseModel):
    """
    删除产品同步模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    product_ids: str = Field(description='需要删除的产品ID，多个用逗号分隔')


class ProductSyncDetailModel(BaseModel):
    """
    产品同步详情模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='产品ID')
    product_name: Optional[str] = Field(default=None, description='产品昵称')
    company_uuid: Optional[str] = Field(default=None, description='公司UUID')
    company_id: Optional[str] = Field(default=None, description='门店ID')
    company_name: Optional[str] = Field(default=None, description='公司昵称')
    service_skill_name: Optional[str] = Field(default=None, description='产品类型')
    service_skill_id: Optional[str] = Field(default=None, description='服务技能ID')
    img_id: Optional[str] = Field(default=None, description='图片ID')
    serve_type_name: Optional[str] = Field(default=None, description='服务类型名称')
    online_store_num: Optional[int] = Field(default=None, description='在线门店数量')
    sum_num: Optional[int] = Field(default=None, description='总数量')
    type_name: Optional[str] = Field(default=None, description='类型名称')
    product_status: Optional[int] = Field(default=None, description='产品状态')
    is_delete: Optional[int] = Field(default=None, description='是否删除')
    details: Optional[str] = Field(default=None, description='产品详情')
    min_number: Optional[int] = Field(default=None, description='最小数量')
    max_number: Optional[int] = Field(default=None, description='最大数量')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class ProductSyncExportModel(BaseModel):
    """
    产品同步导出模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    product_name: Optional[str] = Field(default=None, description='产品昵称')
    company_name: Optional[str] = Field(default=None, description='公司昵称')
    service_skill_name: Optional[str] = Field(default=None, description='产品类型')
    product_status: Optional[int] = Field(default=None, description='产品状态')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
