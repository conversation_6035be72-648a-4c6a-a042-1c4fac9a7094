from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict, Field


class OrderWaiterModel(BaseModel):
    """
    订单派单记录模型
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    order_number: str = Field(description='订单号')
    service_id: str = Field(description='员工id')
    service_name: str = Field(description='员工名称')
    service_personal_commission: Optional[int] = Field(default=0, description='员工分成比例')
    service_personal: Optional[str] = Field(default=None, description='员工分成金额')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    service_start_time: Optional[datetime] = Field(default=None, description='服务开始时间')
    service_end_time: Optional[datetime] = Field(default=None, description='服务结束时间')
    env_before_images: Optional[str] = Field(default=None, description='开始前环境图(file_main_id)')
    service_before_images: Optional[str] = Field(default=None, description='服务前图(file_main_id)')
    service_after_images: Optional[str] = Field(default=None, description='服务后图(file_main_id)')
    signature_images: Optional[str] = Field(default=None, description='电子签图(file_main_id)')


class OrderWaiterWithImagesModel(BaseModel):
    """
    订单派单记录模型（包含图片信息）
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    order_number: str = Field(description='订单号')
    service_id: str = Field(description='员工id')
    service_name: str = Field(description='员工名称')
    service_personal_commission: Optional[int] = Field(default=0, description='员工分成比例')
    service_personal: Optional[str] = Field(default=None, description='员工分成金额')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    service_start_time: Optional[datetime] = Field(default=None, description='服务开始时间')
    service_end_time: Optional[datetime] = Field(default=None, description='服务结束时间')
    
    # 图片信息
    env_before_images: Optional[List[dict]] = Field(default=None, description='开始前环境图列表')
    service_before_images: Optional[List[dict]] = Field(default=None, description='服务前图列表')
    service_after_images: Optional[List[dict]] = Field(default=None, description='服务后图列表')
    signature_images: Optional[List[dict]] = Field(default=None, description='电子签图列表')
