from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class TrainingCoursesModel(BaseModel):
    """
    培训课程表对应的模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[str] = Field(default=None, description='课程ID')
    uuid: Optional[str] = Field(default=None, description='课程唯一标识')
    title: Optional[str] = Field(default=None, description='课程标题')
    description: Optional[str] = Field(default=None, description='课程描述')
    cover_image: Optional[str] = Field(default=None, description='封面图片')
    category_id: Optional[str] = Field(default=None, description='分类ID')
    category_name: Optional[str] = Field(default=None, description='分类名称')
    content_type: Optional[int] = Field(default=None, description='内容类型(1:视频,2:文章,3:图片,4:音频)')
    content_type_name: Optional[str] = Field(default=None, description='内容类型名称')
    content_data: Optional[Dict[str, Any]] = Field(default=None, description='内容数据')
    duration: Optional[int] = Field(default=0, description='时长(秒)')
    views: Optional[int] = Field(default=0, description='浏览次数')
    sort_order: Optional[int] = Field(default=0, description='排序权重')
    is_featured: Optional[int] = Field(default=0, description='是否推荐(1:是,0:否)')
    status: Optional[int] = Field(default=1, description='状态(1:发布,0:下架)')
    creator_id: Optional[str] = Field(default=None, description='创建者ID')
    creator_name: Optional[str] = Field(default=None, description='创建者姓名')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class TrainingCoursesQueryModel(BaseModel):
    """
    培训课程查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    title: Optional[str] = Field(default=None, description='课程标题')
    category_id: Optional[str] = Field(default=None, description='分类ID')
    content_type: Optional[int] = Field(default=None, description='内容类型')
    status: Optional[int] = Field(default=None, description='状态')
    is_featured: Optional[int] = Field(default=None, description='是否推荐')
    creator_name: Optional[str] = Field(default=None, description='创建者姓名')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class TrainingCoursesPageQueryModel(TrainingCoursesQueryModel):
    """
    培训课程分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')





class DeleteTrainingCoursesModel(BaseModel):
    """
    删除培训课程模型
    """
    ids: str = Field(description='需要删除的课程ID，多个用逗号分隔')
