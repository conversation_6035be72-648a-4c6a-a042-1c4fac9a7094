from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class FollowUpRecordModel(BaseModel):
    """
    跟进记录表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='跟进记录ID')
    merchant_id: int = Field(description='商户ID')
    follow_up_person: str = Field(description='跟进人')
    follow_up_time: Optional[datetime] = Field(default=None, description='跟进时间')
    follow_up_content: str = Field(description='跟进内容')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')


class FollowUpRecordQueryModel(BaseModel):
    """
    跟进记录不分页查询对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    merchant_id: Optional[int] = Field(default=None, description='商户ID')
    follow_up_person: Optional[str] = Field(default=None, description='跟进人')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class FollowUpRecordPageQueryModel(FollowUpRecordQueryModel):
    """
    跟进记录分页查询对应pydantic模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页条数')
