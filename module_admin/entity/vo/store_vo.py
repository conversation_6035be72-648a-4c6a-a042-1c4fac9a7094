from datetime import datetime
from typing import Optional, Literal
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from module_admin.annotation.pydantic_annotation import as_query


class StoreModel(BaseModel):
    """
    门店管理模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='门店ID')
    store_uuid: Optional[str] = Field(default=None, description='门店编号')
    company_id: Optional[str] = Field(default=None, description='公司ID')
    name: str = Field(..., description='门店名称')
    phone: Optional[str] = Field(default=None, description='联系电话')
    mobile: Optional[str] = Field(default=None, description='门店电话')
    address: Optional[str] = Field(default=None, description='地址')
    business_hours: Optional[str] = Field(default=None, description='营业时间')
    manager: Optional[str] = Field(default=None, description='负责人')
    status: Optional[int] = Field(default=1, description='状态：0-关闭，1-营业')
    store_status: Optional[int] = Field(default=1, description='门店状态')
    remark: Optional[str] = Field(default=None, description='备注')
    introduce: Optional[str] = Field(default=None, description='门店简介')
    email: Optional[str] = Field(default=None, description='邮箱')
    is_new: Optional[int] = Field(default=None, description='是否新店')
    level: Optional[str] = Field(default=None, description='门店等级')
    flag: Optional[int] = Field(default=0, description='标记')
    is_show_wxapp: Optional[int] = Field(default=1, description='是否显示在微信小程序')
    open_shop_name: Optional[str] = Field(default=None, description='开放店铺名称')
    open_shop_uuid: Optional[str] = Field(default=None, description='第三方关联门店ID')
    dredge_date: Optional[datetime] = Field(default=None, description='开通日期')
    expiry_date: Optional[datetime] = Field(default=None, description='到期日期')
    insurance_company: Optional[str] = Field(default=None, description='保险公司')
    user_count: Optional[int] = Field(default=None, description='用户数量')
    is_delete: Optional[int] = Field(default=0, description='是否删除(1为已删除，0为未删除)')
    newhome_id: Optional[str] = Field(default=None, description='新家ID')
    created_by: Optional[str] = Field(default=None, description='创建人')
    updated_by: Optional[str] = Field(default=None, description='修改人')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')


class StoreQueryModel(BaseModel):
    """
    门店不分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    name: Optional[str] = Field(default=None, description='门店名称')
    manager: Optional[str] = Field(default=None, description='负责人')
    status: Optional[int] = Field(default=None, description='状态：0-关闭，1-营业')
    company_id: Optional[str] = Field(default=None, description='公司ID')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class StorePageQueryModel(StoreQueryModel):
    """
    门店管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页条数')


class DeleteStoreModel(BaseModel):
    """
    删除门店模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    ids: str = Field(description='需要删除的门店ID')
