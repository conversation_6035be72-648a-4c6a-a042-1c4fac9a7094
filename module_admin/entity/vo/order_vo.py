from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank
from typing import Optional
from module_admin.annotation.pydantic_annotation import as_query


class OrderModel(BaseModel):
    """
    订单表对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    order_number: Optional[str] = Field(default=None, description='订单编号')
    serve_number: Optional[str] = Field(default=None, description='服务编号')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    mobile: Optional[str] = Field(default=None, description='用户手机号')
    store_id: Optional[int] = Field(default=None, description='门店ID')
    store_name: Optional[str] = Field(default=None, description='门店名称')
    store_uuid: Optional[str] = Field(default=None, description='门店UUID')
    product_id: Optional[int] = Field(default=None, description='产品ID')
    product_name: Optional[str] = Field(default=None, description='产品名称')
    product_type: Optional[int] = Field(default=None, description='产品类型: 1-单次')
    product_type_name: Optional[str] = Field(default=None, description='产品类型名称')
    product_unit: Optional[str] = Field(default=None, description='产品单位')
    service_address: Optional[str] = Field(default=None, description='服务地址')
    address_id: Optional[int] = Field(default=None, description='服务地址ID')
    channel_number: Optional[str] = Field(default=None, description='渠道编号')
    service_phone: Optional[str] = Field(default=None, description='服务环境照片JSON')
    order_status: Optional[str] = Field(default=None, description='订单状态名称')
    order_status_name: Optional[str] = Field(default=None, description='订单状态名称')
    service_type: Optional[int] = Field(default=None, description='服务类型:1-上门')
    service_type_name: Optional[str] = Field(default=None, description='服务类型名称')
    remark: Optional[str] = Field(default=None, description='客户备注')
    after_sale_remark: Optional[str] = Field(default=None, description='售后备注')
    service_remark: Optional[str] = Field(default=None, description='服务提醒')
    service_date: Optional[datetime] = Field(default=None, description='服务时间')
    recommend_level: Optional[int] = Field(default=None, description='优先级:0-未设置,1-普通,2-重要,3-紧急,4-延后')
    founder_type: Optional[str] = Field(default=None, description='创建类型')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    buy_num: Optional[int] = Field(default=None, description='购买数量')
    pay_actual: Optional[Decimal] = Field(default=None, description='实际支付金额')
    source: Optional[str] = Field(default=None, description='订单来源')
    total_pay_actual: Optional[Decimal] = Field(default=None, description='总支付金额-订单金额')
    service_hour: Optional[str] = Field(default=None, description='服务时间（小时）')
    service_personal: Optional[str] = Field(default=None, description='预派服务人员id 列表')
    pay_type: Optional[str] = Field(default=None, description='支付类型')
    sale_user_commission: Optional[Decimal] = Field(default=None, description='销售人员佣金')
    sale_user_uuid: Optional[str] = Field(default=None, description='销售人员UUID')
    used_time: Optional[int] = Field(default=None, description='使用时间')
    product_sku_id: Optional[int] = Field(default=None, description='skuid')
    coupon_id: Optional[int] = Field(default=None, description='优惠券id')
    endaddress_id: Optional[int] = Field(default=None, description='服务结束地址  针对搬家')


class OrderQueryModel(OrderModel):
    """
    订单管理不分页查询模型
    """

    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')
    service_name: Optional[str] = Field(default=None, description='服务人员姓名')
    service_address: Optional[str] = Field(default=None, description='服务地址')


@as_query
class OrderPageQueryModel(OrderQueryModel):
    """
    订单管理分页查询模型
    """

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class AddOrderModel(BaseModel):
    """
    新增订单模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    order_number: str = Field(description='订单编号')
    serve_number: Optional[str] = Field(default=None, description='服务编号')
    user_id: int = Field(description='用户ID')
    store_id: int = Field(description='门店ID')
    store_name: Optional[str] = Field(default=None, description='门店名称')
    store_uuid: Optional[str] = Field(default=None, description='门店UUID')
    product_id: Optional[int] = Field(default=None, description='产品ID')
    product_name: Optional[str] = Field(default=None, description='产品名称')
    product_type: Optional[int] = Field(default=None, description='产品类型: 1-单次')
    product_type_name: Optional[str] = Field(default=None, description='产品类型名称')
    product_unit: Optional[str] = Field(default=None, description='产品单位')
    service_address: Optional[str] = Field(default=None, description='服务地址')
    address_id: Optional[int] = Field(default=None, description='服务地址ID')
    channel_number: Optional[str] = Field(default=None, description='渠道编号')
    service_phone: Optional[str] = Field(default=None, description='服务环境照片JSON')
    order_status: Optional[str] = Field(default='派单待确认', description='订单状态名称')
    order_status_name: Optional[str] = Field(default=None, description='订单状态名称')
    service_type: Optional[int] = Field(default=1, description='服务类型:1-上门')
    service_type_name: Optional[str] = Field(default=None, description='服务类型名称')
    remark: Optional[str] = Field(default=None, description='客户备注')
    after_sale_remark: Optional[str] = Field(default=None, description='售后备注')
    service_remark: Optional[str] = Field(default=None, description='服务提醒')
    service_date: Optional[datetime] = Field(default=None, description='服务时间')
    recommend_level: Optional[int] = Field(default=0, description='优先级:0-未设置,1-普通,2-重要,3-紧急,4-延后')
    founder_type: Optional[str] = Field(default=None, description='创建类型')
    buy_num: int = Field(description='购买数量')
    pay_actual: Decimal = Field(description='实际支付金额')
    source: str = Field(description='订单来源')
    total_pay_actual: Decimal = Field(description='总支付金额-订单金额')
    service_hour: str = Field(description='服务时间（小时）')
    service_personal: Optional[str] = Field(default=None, description='预派服务人员id 列表')
    pay_type: Optional[str] = Field(default=None, description='支付类型')
    sale_user_commission: Optional[Decimal] = Field(default=0.00, description='销售人员佣金')
    sale_user_uuid: Optional[str] = Field(default=None, description='销售人员UUID')
    used_time: Optional[int] = Field(default=None, description='使用时间')
    product_sku_id: Optional[int] = Field(default=None, description='skuid')
    coupon_id: Optional[int] = Field(default=None, description='优惠券id')
    endaddress_id: Optional[int] = Field(default=None, description='服务结束地址  针对搬家')

    @NotBlank(field_name='order_number', message='订单编号不能为空')
    def get_order_number(self):
        return self.order_number

    def validate_fields(self):
        self.get_order_number()


class EditOrderModel(AddOrderModel):
    """
    编辑订单模型
    """

    id: int = Field(description='订单ID')


class UpdateOrderRemarkModel(BaseModel):
    """
    更新订单备注模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    id: int = Field(description='订单ID')
    remark: Optional[str] = Field(default=None, description='客户备注')
    after_sale_remark: Optional[str] = Field(default=None, description='售后备注')
    service_remark: Optional[str] = Field(default=None, description='服务提醒')
    service_date: Optional[datetime] = Field(default=None, description='服务时间')


class DeleteOrderModel(BaseModel):
    """
    删除订单模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    ids: str = Field(description='需要删除的订单ID')
