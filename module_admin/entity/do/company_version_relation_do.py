from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, DECIMAL, SmallInteger, BigInteger
from config.database import Base


class CompanyVersionRelation(Base):
    """
    公司版本关联表
    """

    __tablename__ = 'company_version_relation'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    company_uuid = Column(String(36), nullable=False, comment='公司UUID')
    version_uuid = Column(String(36), nullable=False, comment='版本UUID')
    purchase_time = Column(DateTime, nullable=False, default=datetime.now(), comment='购买时间')
    expire_time = Column(DateTime, nullable=False, comment='过期时间')
    status = Column(SmallInteger, nullable=False, default=1, comment='状态')
    price = Column(DECIMAL(10, 2), nullable=True, comment='价格')
    order_no = Column(String(64), nullable=True, comment='订单号')
    create_time = Column(DateTime, nullable=False, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now(), onupdate=datetime.now(), comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
