from datetime import datetime
from sqlalchemy import Column, DateTime, String, DECIMAL, Integer, BigInteger, Text, SmallInteger
from config.database import Base


class Order(Base):
    """
    订单表
    """

    __tablename__ = 'order'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    order_number = Column(String(64), nullable=False, unique=True, comment='订单编号')
    serve_number = Column(String(64), nullable=True, comment='服务编号')
    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    store_id = Column(Integer, nullable=False, comment='门店ID')
    store_name = Column(String(100), nullable=True, comment='门店名称')
    store_uuid = Column(String(64), nullable=True, comment='门店UUID')
    product_id = Column(Integer, nullable=True, comment='产品ID')
    product_name = Column(String(100), nullable=True, comment='产品名称')
    product_type = Column(Integer, nullable=True, comment='产品类型: 1-单次')
    product_type_name = Column(String(20), nullable=True, comment='产品类型名称')
    product_unit = Column(String(20), nullable=True, comment='产品单位')
    service_address = Column(String(255), nullable=True, comment='服务地址')
    address_id = Column(BigInteger, nullable=True, comment='服务地址ID')
    channel_number = Column(String(64), nullable=True, comment='渠道编号')
    service_phone = Column(Text, nullable=True, comment='服务环境照片JSON')
    order_status = Column(SmallInteger, nullable=True, default=1, comment='订单状态:1-待支付等')
    order_status_name = Column(String(20), nullable=True, comment='订单状态名称')
    service_type = Column(SmallInteger, nullable=True, default=1, comment='服务类型:1-上门')
    service_type_name = Column(String(20), nullable=True, comment='服务类型名称')
    remark = Column(Text, nullable=True, comment='客户备注')
    after_sale_remark = Column(Text, nullable=True, comment='售后备注')
    service_remark = Column(Text, nullable=True, comment='服务提醒')
    service_date = Column(DateTime, nullable=True, comment='服务时间')
    recommend_level = Column(SmallInteger, nullable=True, default=0, comment='优先级:0-未设置,1-普通,2-重要,3-紧急,4-延后')
    founder_type = Column(String(10), nullable=True, comment='创建类型')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    buy_num = Column(Integer, nullable=False, comment='购买数量')
    pay_actual = Column(DECIMAL(10, 2), nullable=False, comment='实际支付金额')
    source = Column(String(50), nullable=False, comment='订单来源')
    total_pay_actual = Column(DECIMAL(10, 2), nullable=False, comment='总支付金额-订单金额')
    service_hour = Column(String(10), nullable=False, comment='服务时间（小时）')
    service_personal = Column(String(255), nullable=True, comment='预派服务人员id 列表')
    pay_type = Column(String(20), nullable=True, comment='支付类型')
    sale_user_commission = Column(DECIMAL(10, 2), nullable=True, default=0.00, comment='销售人员佣金')
    sale_user_uuid = Column(String(64), nullable=True, comment='销售人员UUID')
    used_time = Column(Integer, nullable=True, comment='使用时间')
    product_sku_id = Column(Integer, nullable=True, comment='skuid')
    coupon_id = Column(Integer, nullable=True, comment='优惠券id')
    endaddress_id = Column(Integer, nullable=True, comment='服务结束地址  针对搬家')
