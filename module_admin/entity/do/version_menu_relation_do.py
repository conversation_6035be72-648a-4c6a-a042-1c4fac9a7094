from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, SmallInteger, BigInteger
from config.database import Base


class VersionMenuRelation(Base):
    """
    软件版本菜单关联表
    """

    __tablename__ = 'version_menu_relation'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    version_uuid = Column(String(36), nullable=False, comment='软件版本UUID')
    menu_uuid = Column(String(36), nullable=False, comment='菜单UUID')
    status = Column(SmallInteger, nullable=False, default=1, comment='状态(0禁用 1启用)')
    create_time = Column(DateTime, nullable=False, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now(), onupdate=datetime.now(), comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
