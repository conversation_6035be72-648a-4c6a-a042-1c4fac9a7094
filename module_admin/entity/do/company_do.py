from datetime import datetime
from sqlalchemy import Column, DateTime, String, DECIMAL
from config.database import Base


class Company(Base):
    """
    公司信息表
    """

    __tablename__ = 'company'

    id = Column(String(64), primary_key=True, comment='公司ID')
    name = Column(String(100), nullable=False, comment='公司名称')
    city_id = Column(String(20), nullable=True, comment='城市ID')
    city = Column(String(50), nullable=True, comment='城市')
    address = Column(String(255), nullable=True, comment='地址')
    address_desc = Column(String(255), nullable=True, comment='地址描述')
    balance = Column(DECIMAL(15, 2), nullable=False, default=0.00, comment='公司余额')
    frozen_amount = Column(DECIMAL(15, 2), nullable=False, default=0.00, comment='冻结资金')
    withdrawal_fee_rate = Column(DECIMAL(5, 4), nullable=False, default=0.2000, comment='提现费率，默认20%')
    split_fee_rate = Column(DECIMAL(5, 4), nullable=False, default=0.0500, comment='分账费率，默认5%')
    status = Column(String(2), nullable=True, default='1', comment='状态')
    is_delete = Column(String(2), nullable=True, default='0', comment='是否删除')
    create_time = Column(DateTime, nullable=True, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
