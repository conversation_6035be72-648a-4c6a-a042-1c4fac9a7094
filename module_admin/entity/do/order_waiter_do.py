from datetime import datetime
from sqlalchemy import Column, DateTime, String, Integer, DECIMAL
from config.database import Base


class OrderWaiter(Base):
    """
    订单派单记录表
    """

    __tablename__ = 'order_waiter'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    order_number = Column(String(500), nullable=False, comment='订单号')
    service_id = Column(String(500), nullable=False, comment='员工id')
    service_name = Column(String(500), nullable=False, comment='员工名称')
    service_personal_commission = Column(Integer, default=0, comment='员工分成比例')
    service_personal = Column(String(255), nullable=True, comment='员工分成金额')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    service_start_time = Column(DateTime, nullable=True, comment='服务开始时间')
    service_end_time = Column(DateTime, nullable=True, comment='服务结束时间')
    env_before_images = Column(String(36), nullable=True, comment='开始前环境图(file_main_id)')
    service_before_images = Column(String(36), nullable=True, comment='服务前图(file_main_id)')
    service_after_images = Column(String(36), nullable=True, comment='服务后图(file_main_id)')
    signature_images = Column(String(36), nullable=True, comment='电子签图(file_main_id)')
