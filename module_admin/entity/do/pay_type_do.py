from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text
from config.database import Base


class PayType(Base):
    """
    支付类型表
    """

    __tablename__ = 'pay_type'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    company_uuid = Column(String(64), nullable=False, comment='公司UUID')
    pay_type_code = Column(String(30), nullable=False, comment='支付类型代码')
    pay_type_name = Column(String(50), nullable=False, comment='支付类型名称')
    icon_url = Column(String(255), nullable=True, comment='图标地址')
    is_enabled = Column(Integer, nullable=False, default=1, comment='是否启用(1:启用,0:禁用)')
    is_preset = Column(Integer, nullable=False, default=0, comment='是否预设(1:预设,0:自定义)')
    channel_config = Column(Text, nullable=True, comment='渠道配置(JSON格式)')
    sort_order = Column(Integer, nullable=True, default=0, comment='排序序号')
    remark = Column(String(500), nullable=True, comment='备注')
    created_by = Column(String(64), nullable=False, comment='创建人')
    updated_by = Column(String(64), nullable=True, comment='更新人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, comment='更新时间')
