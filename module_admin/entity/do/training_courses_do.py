from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, DateTime, SmallInteger, JSON
from config.database import Base


class TrainingCourses(Base):
    """
    培训课程表
    """
    __tablename__ = 'training_courses'

    id = Column(String(64), primary_key=True, comment='课程ID')
    uuid = Column(String(64), nullable=False, comment='课程唯一标识')
    title = Column(String(200), nullable=False, comment='课程标题')
    description = Column(Text, comment='课程描述')
    cover_image = Column(String(255), comment='封面图片')
    category_id = Column(String(64), comment='分类ID')
    content_type = Column(SmallInteger, nullable=False, comment='内容类型(1:视频,2:文章,3:图片,4:音频)')
    content_data = Column(JSON, comment='内容数据')
    duration = Column(Integer, default=0, comment='时长(秒)')
    views = Column(Integer, default=0, comment='浏览次数')
    sort_order = Column(Integer, default=0, comment='排序权重')
    is_featured = Column(SmallInteger, default=0, comment='是否推荐(1:是,0:否)')
    status = Column(SmallInteger, default=1, comment='状态(1:发布,0:下架)')
    creator_id = Column(String(64), comment='创建者ID')
    creator_name = Column(String(50), comment='创建者姓名')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
