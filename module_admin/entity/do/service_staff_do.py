from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String
from config.database import Base


class ServiceStaff(Base):
    """
    服务员工表
    """

    __tablename__ = 'service_staff'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='员工ID')
    user_name = Column(String(50), nullable=False, comment='员工昵称')
    store_uuid = Column(String(64), nullable=False, comment='门店UUID')
    mobile = Column(String(20), nullable=True, comment='手机号')
    status = Column(String(2), default='1', comment='状态（1正常 0停用）')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
