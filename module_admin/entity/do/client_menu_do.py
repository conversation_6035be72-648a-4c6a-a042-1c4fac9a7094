from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, SmallInteger, BigInteger
from config.database import Base


class ClientMenu(Base):
    """
    客户端菜单表
    """

    __tablename__ = 'client_menu'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    uuid = Column(String(36), nullable=False, unique=True, comment='UUID')
    menu_name = Column(String(50), nullable=False, comment='菜单名称')
    path = Column(String(200), nullable=True, comment='路径')
    link_type = Column(SmallInteger, nullable=False, default=0, comment='链接类型(0内部链接 1外部链接)')
    icon_url = Column(String(255), nullable=True, comment='图标URL')
    sort_order = Column(Integer, nullable=True, default=0, comment='排序')
    status = Column(SmallInteger, nullable=True, default=1, comment='状态')
    app_id = Column(String(64), nullable=True, comment='应用ID')
    extra_data = Column(String(500), nullable=True, comment='额外数据')
    create_time = Column(DateTime, nullable=False, default=datetime.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now(), onupdate=datetime.now(), comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
