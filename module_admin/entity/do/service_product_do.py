from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String
from config.database import Base


class ServiceProduct(Base):
    """
    服务产品关联表
    """

    __tablename__ = 'service_product'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    productid = Column(Integer, nullable=False, comment='产品ID')
    staff_id = Column(Integer, nullable=False, comment='员工ID')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
