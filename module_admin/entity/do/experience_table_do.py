from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, DECIMAL, SmallInteger
from config.database import Base


class ExperienceTable(Base):
    """
    商户信息表
    """

    __tablename__ = 'experience_table'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='商户ID')
    company_name = Column(String(64), nullable=True, comment='公司名称')
    license_image = Column(String(200), nullable=True, comment='营业执照图片')
    license_name = Column(String(24), nullable=True, comment='营业执照名称')
    license_code = Column(String(63), nullable=True, comment='营业执照代码')
    legal_address = Column(String(64), nullable=True, comment='法定地址')
    legal_person_name = Column(String(64), nullable=True, comment='法人姓名')
    phone_number = Column(String(64), nullable=True, comment='电话号码')
    selected_address = Column(String(64), nullable=True, comment='选择的地址')
    detail_address = Column(String(64), nullable=True, comment='详细地址')
    referrer = Column(String(64), nullable=True, comment='推荐人')
    selected_products = Column(String(64), nullable=True, comment='选择的产品')
    total_price = Column(DECIMAL(10, 2), nullable=True, comment='总价')
    is_all_selected = Column(SmallInteger, nullable=True, comment='是否全选')
    period_type = Column(String(64), nullable=True, comment='周期类型')
    license_expiry_date = Column(DateTime, nullable=True, comment='营业执照到期日期')
    status = Column(SmallInteger, nullable=True, default=0, comment='状态(0待审核 1通过 2未通过)')
