from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text
from config.database import Base


class FollowUpRecord(Base):
    """
    跟进记录表
    """

    __tablename__ = 'follow_up_record'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='跟进记录ID')
    merchant_id = Column(Integer, nullable=False, comment='商户ID')
    follow_up_person = Column(String(64), nullable=False, comment='跟进人')
    follow_up_time = Column(DateTime, nullable=False, default=datetime.now(), comment='跟进时间')
    follow_up_content = Column(Text, nullable=False, comment='跟进内容')
    create_by = Column(String(64), nullable=True, default='', comment='创建者')
    create_time = Column(DateTime, nullable=True, default=datetime.now(), comment='创建时间')
