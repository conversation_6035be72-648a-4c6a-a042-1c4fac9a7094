from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, DECIMAL, SmallInteger, BigInteger
from config.database import Base


class SoftwareVersion(Base):
    """
    软件版本表
    """

    __tablename__ = 'software_version'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    uuid = Column(String(36), nullable=False, unique=True, comment='UUID')
    name = Column(String(50), nullable=False, unique=True, comment='版本名称')
    description = Column(String(255), nullable=False, comment='版本描述')
    price = Column(DECIMAL(10, 2), nullable=False, comment='价格')
    is_available = Column(SmallInteger, nullable=False, default=1, comment='是否可用')
    sort_order = Column(Integer, nullable=True, default=0, comment='排序')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    remark = Column(String(500), nullable=True, comment='备注')
