from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, SmallInteger, DECIMAL, BigInteger
from config.database import Base


class StoreInfo(Base):
    """
    门店详细信息表
    """

    __tablename__ = 'store_info'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    store_id = Column(BigInteger, nullable=False, comment='门店ID')
    type = Column(String(255), nullable=True, comment='类型')
    nature = Column(String(255), nullable=True, comment='性质')
    unit_nature = Column(String(50), nullable=True, comment='单位性质')
    business_license = Column(String(255), nullable=True, comment='营业执照')
    store_specifications = Column(String(255), nullable=True, comment='门店规格')
    desc_phone_type = Column(String(255), nullable=True, comment='描述电话类型')
    website_phone_type = Column(String(255), nullable=True, comment='网站电话类型')
    is_invoice = Column(SmallInteger, nullable=True, comment='是否开票')
    invoice_title = Column(String(155), nullable=True, comment='发票抬头')
    invoice_code = Column(String(255), nullable=True, comment='发票代码')
    invoice_phone = Column(String(20), nullable=True, comment='发票电话')
    invoice_address = Column(String(255), nullable=True, comment='发票地址')
    invoice_bank = Column(String(100), nullable=True, comment='发票银行')
    invoice_account = Column(String(100), nullable=True, comment='发票账户')
    is_bd_pos = Column(SmallInteger, nullable=True, comment='是否BD POS')
    location_synchronize_status = Column(SmallInteger, nullable=True, comment='位置同步状态')
    health_degree = Column(DECIMAL(5, 2), nullable=True, comment='健康度')
    is_clean_keeping = Column(SmallInteger, nullable=True, default=0, comment='是否保洁')
    is_housekeeping_staff = Column(SmallInteger, nullable=True, default=0, comment='是否家政人员')
    is_part_time_job = Column(SmallInteger, nullable=True, default=0, comment='是否兼职')
    is_free = Column(SmallInteger, nullable=True, default=0, comment='是否免费')
    is_discount = Column(SmallInteger, nullable=True, default=0, comment='是否折扣')
    user_num_limit = Column(Integer, nullable=True, comment='用户数量限制')
    pay_num_limit = Column(Integer, nullable=True, comment='支付数量限制')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
