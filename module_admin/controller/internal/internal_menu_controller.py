from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.vo.internal_menu_vo import InternalMenuModel, InternalMenuPageQueryModel, DeleteInternalMenuModel, InternalMenuQueryModel
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.internal.internal_menu_service import InternalMenuService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel
from utils.common_util import CamelCaseUtil
from pydantic_validation_decorator import ValidateFields
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import Current<PERSON>serModel
from module_admin.service.login_service import LoginService
from utils.log_util import logger

router = APIRouter(prefix="/internal/menu", tags=["内部菜单管理"], dependencies=[Depends(LoginService.get_current_user)])
internalMenuController = router


@router.get("/list", response_model=List[InternalMenuModel], dependencies=[Depends(CheckUserInterfaceAuth('system:internal:menu:list'))])
async def get_internal_menu_list(
    request: Request,
    menu_query: InternalMenuQueryModel = Depends(InternalMenuQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取内部菜单列表
    :param request: 请求对象
    :param menu_query: 菜单查询条件
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 内部菜单列表
    """
    menu_query_result = await InternalMenuService.get_menu_list_services(query_db, menu_query, current_user)
    logger.info('获取成功')

    return ResponseUtil.success(data=menu_query_result)


@router.get("/{menu_id}", response_model=InternalMenuModel, dependencies=[Depends(CheckUserInterfaceAuth('system:internal:menu:query'))])
async def query_detail_internal_menu(request: Request, menu_id: int, query_db: AsyncSession = Depends(get_db)):
    """
    获取内部菜单详细信息
    :param request: 请求对象
    :param menu_id: 菜单ID
    :param query_db: 数据库会话
    :return: 内部菜单信息
    """
    menu_detail_result = await InternalMenuService.menu_detail_services(query_db, menu_id)
    logger.info(f'获取menu_id为{menu_id}的信息成功')

    return ResponseUtil.success(data=menu_detail_result)


@router.post("", dependencies=[Depends(CheckUserInterfaceAuth('system:internal:menu:add'))])
@ValidateFields(validate_model='add_menu')
@Log(title='内部菜单管理', business_type=BusinessType.INSERT)
async def add_internal_menu(
    request: Request,
    add_menu: InternalMenuModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增内部菜单
    :param request: 请求对象
    :param add_menu: 新增菜单信息
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 响应结果
    """
    add_menu.create_time = datetime.now()
    add_menu.update_time = datetime.now()
    add_menu_result = await InternalMenuService.add_menu_services(query_db, add_menu)
    logger.info(add_menu_result.message)

    return ResponseUtil.success(msg=add_menu_result.message)


@router.put("", dependencies=[Depends(CheckUserInterfaceAuth('system:internal:menu:edit'))])
@ValidateFields(validate_model='edit_menu')
@Log(title='内部菜单管理', business_type=BusinessType.UPDATE)
async def edit_internal_menu(
    request: Request,
    edit_menu: InternalMenuModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    修改内部菜单
    :param request: 请求对象
    :param edit_menu: 修改菜单信息
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 响应结果
    """
    edit_menu.update_time = datetime.now()
    edit_menu_result = await InternalMenuService.edit_menu_services(query_db, edit_menu)
    logger.info(edit_menu_result.message)

    return ResponseUtil.success(msg=edit_menu_result.message)


@router.delete("/{menu_ids}", dependencies=[Depends(CheckUserInterfaceAuth('system:internal:menu:remove'))])
@Log(title='内部菜单管理', business_type=BusinessType.DELETE)
async def delete_internal_menu(request: Request, menu_ids: str, query_db: AsyncSession = Depends(get_db)):
    """
    删除内部菜单
    :param request: 请求对象
    :param menu_ids: 菜单ID串
    :param query_db: 数据库会话
    :return: 响应结果
    """
    delete_menu = DeleteInternalMenuModel(menuIds=menu_ids)
    delete_menu_result = await InternalMenuService.delete_menu_services(query_db, delete_menu)
    logger.info(delete_menu_result.message)

    return ResponseUtil.success(msg=delete_menu_result.message)


@router.get('/treeselect')
async def get_internal_menu_tree(
    request: Request,
    client_type: Optional[int] = Query(None, description="客户端类型: 0 sys网页端 1 store门店端"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取内部菜单树
    :param request: 请求对象
    :param client_type: 客户端类型
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 内部菜单树
    """
    menu_query_result = await InternalMenuService.get_menu_tree_services(query_db, client_type, current_user)
    logger.info('获取成功')

    return ResponseUtil.success(data=menu_query_result)


@router.get('/roleMenuTreeselect/{role_id}')
async def get_internal_role_menu_tree(
    request: Request,
    role_id: int,
    client_type: Optional[int] = Query(None, description="客户端类型: 0 sys网页端 1 store门店端"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    根据角色ID查询内部菜单树
    :param request: 请求对象
    :param role_id: 角色ID
    :param client_type: 客户端类型
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 内部菜单树及选中节点
    """
    role_menu_query_result = await InternalMenuService.get_role_menu_tree_services(query_db, role_id, client_type, current_user)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=role_menu_query_result) 