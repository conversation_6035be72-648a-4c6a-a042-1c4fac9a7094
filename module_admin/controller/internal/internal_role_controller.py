from datetime import datetime
from typing import Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.vo.internal_role_vo import InternalRoleModel, InternalRolePageQueryModel, DeleteInternalRoleModel
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.internal.internal_role_service import InternalRoleService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel
from utils.common_util import CamelCaseUtil

router = APIRouter(prefix="/internal/role", tags=["内部角色管理"])


@router.get("/list", response_model=PageResponseModel)
async def get_role_list(
    pageNum: int = Query(1, description="页码"),
    pageSize: int = Query(10, description="每页数量"),
    roleName: Optional[str] = Query(None, description="角色名称"),
    roleKey: Optional[str] = Query(None, description="权限字符"),
    status: Optional[str] = Query(None, description="状态"),
    beginTime: Optional[str] = Query(None, description="开始时间"),
    endTime: Optional[str] = Query(None, description="结束时间"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取内部角色列表
    :param pageNum: 页码
    :param pageSize: 每页数量
    :param roleName: 角色名称
    :param roleKey: 权限字符
    :param status: 状态
    :param beginTime: 开始时间
    :param endTime: 结束时间
    :param query_db: 数据库会话
    :return: 内部角色列表
    """
    query_params = InternalRolePageQueryModel(
        pageNum=pageNum,
        pageSize=pageSize,
        roleName=roleName,
        roleKey=roleKey,
        status=status,
        beginTime=beginTime,
        endTime=endTime
    )
    result = await InternalRoleService.get_role_list_services(query_db, query_params)
    return result


@router.get("/{role_id}", response_model=InternalRoleModel)
async def get_role_detail(
    role_id: int,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取内部角色详细信息
    :param role_id: 角色ID
    :param query_db: 数据库会话
    :return: 内部角色信息
    """
    result = await InternalRoleService.role_detail_services(query_db, role_id)
    return result


@router.post("/add", response_model=CrudResponseModel)
async def add_role(
    add_role: InternalRoleModel,
    query_db: AsyncSession = Depends(get_db)
):
    """
    新增内部角色
    :param add_role: 新增角色信息
    :param query_db: 数据库会话
    :return: 响应结果
    """
    result = await InternalRoleService.add_role_services(query_db, add_role)
    return result


@router.put("/edit", response_model=CrudResponseModel)
async def edit_role(
    edit_role: InternalRoleModel,
    query_db: AsyncSession = Depends(get_db)
):
    """
    修改内部角色
    :param edit_role: 修改角色信息
    :param query_db: 数据库会话
    :return: 响应结果
    """
    result = await InternalRoleService.edit_role_services(query_db, edit_role)
    return result


@router.delete("/delete", response_model=CrudResponseModel)
async def delete_role(
    roleIds: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    删除内部角色
    :param roleIds: 角色ID串
    :param query_db: 数据库会话
    :return: 响应结果
    """
    delete_role = DeleteInternalRoleModel(
        roleIds=roleIds,
        updateTime=datetime.now()
    )
    result = await InternalRoleService.delete_role_services(query_db, delete_role)
    return result


@router.delete("/{role_id}", response_model=CrudResponseModel)
async def delete_role_by_id(
    role_id: int,
    query_db: AsyncSession = Depends(get_db)
):
    """
    删除单个内部角色（RESTful风格）
    :param role_id: 角色ID
    :param query_db: 数据库会话
    :return: 响应结果
    """
    delete_role = DeleteInternalRoleModel(
        roleIds=str(role_id),
        updateTime=datetime.now()
    )
    result = await InternalRoleService.delete_role_services(query_db, delete_role)
    return result


@router.put("/{role_id}/status/{status}", response_model=CrudResponseModel)
async def update_role_status(
    role_id: int,
    status: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    修改内部角色状态（RESTful风格）
    :param role_id: 角色ID
    :param status: 状态(0-启用,1-停用)
    :param query_db: 数据库会话
    :return: 响应结果
    """
    # 创建角色对象，仅包含需要更新的字段
    role_model = InternalRoleModel(
        roleId=role_id,
        status=status,
        updateTime=datetime.now()
    )
    # 调用现有服务方法更新状态
    result = await InternalRoleService.update_role_status_service(query_db, role_model)
    return result 