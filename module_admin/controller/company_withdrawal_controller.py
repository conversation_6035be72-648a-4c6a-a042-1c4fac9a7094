from fastapi import APIRouter, Depends, Request, Path
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.company_withdrawal_vo import (
    CompanyWithdrawalPageQueryModel,
    CompanyWithdrawalCreateModel,
    CompanyWithdrawalReviewModel,
    CompanyWithdrawalProcessModel,
    CompanyWithdrawalResponseModel
)
from module_admin.service.company_withdrawal_service import CompanyWithdrawalService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


companyWithdrawalController = APIRouter(
    prefix='/finance/withdrawal',
    dependencies=[Depends(LoginService.get_current_user)]
)


@companyWithdrawalController.get(
    '/list', 
    response_model=PageResponseModel, 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:withdrawal:list'))]
)
async def get_company_withdrawal_list(
    request: Request,
    withdrawal_page_query: CompanyWithdrawalPageQueryModel = Depends(CompanyWithdrawalPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取公司提现申请列表
    
    :param request: 请求对象
    :param withdrawal_page_query: 分页查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 公司提现申请列表
    """
    try:
        # 获取分页数据
        withdrawal_page_query_result = await CompanyWithdrawalService.get_company_withdrawal_list_services(
            query_db, withdrawal_page_query, is_page=True
        )
        logger.info('获取公司提现申请列表成功')

        return ResponseUtil.success(model_content=withdrawal_page_query_result)
    except Exception as e:
        logger.error(f'获取公司提现申请列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取公司提现申请列表失败: {str(e)}')


@companyWithdrawalController.get(
    '/{withdrawal_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:withdrawal:query'))]
)
async def get_company_withdrawal_detail(
    request: Request,
    withdrawal_id: int = Path(..., description="提现申请ID"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取公司提现申请详情
    
    :param request: 请求对象
    :param withdrawal_id: 提现申请ID
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 公司提现申请详情
    """
    try:
        withdrawal_detail_result = await CompanyWithdrawalService.get_company_withdrawal_detail_services(
            query_db, withdrawal_id
        )
        
        if not withdrawal_detail_result:
            return ResponseUtil.error(msg='提现申请不存在')
            
        logger.info(f'获取公司提现申请详情成功，申请ID: {withdrawal_id}')

        return ResponseUtil.success(data=withdrawal_detail_result)
    except Exception as e:
        logger.error(f'获取公司提现申请详情失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取公司提现申请详情失败: {str(e)}')


@companyWithdrawalController.post(
    '/apply',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:withdrawal:add'))]
)
@Log(title='提现申请', business_type=BusinessType.INSERT)
async def create_withdrawal_application(
    request: Request,
    withdrawal_data: CompanyWithdrawalCreateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    创建提现申请
    
    :param request: 请求对象
    :param withdrawal_data: 提现申请数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 创建结果
    """
    try:
        create_result = await CompanyWithdrawalService.create_withdrawal_application_services(
            query_db, withdrawal_data, current_user
        )
        logger.info(f'创建提现申请成功，申请单号: {create_result["withdrawal_no"]}')

        return ResponseUtil.success(data=create_result, msg='提现申请提交成功')
    except Exception as e:
        logger.error(f'创建提现申请失败: {str(e)}')
        return ResponseUtil.error(msg=str(e))


@companyWithdrawalController.post(
    '/{withdrawal_id}/review',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:withdrawal:review'))]
)
@Log(title='提现审核', business_type=BusinessType.UPDATE)
async def review_withdrawal_application(
    request: Request,
    withdrawal_id: int = Path(..., description="提现申请ID"),
    review_data: CompanyWithdrawalReviewModel = None,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    审核提现申请
    
    :param request: 请求对象
    :param withdrawal_id: 提现申请ID
    :param review_data: 审核数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 审核结果
    """
    try:
        review_result = await CompanyWithdrawalService.review_withdrawal_application_services(
            query_db, withdrawal_id, review_data, current_user
        )
        logger.info(f'审核提现申请成功，申请ID: {withdrawal_id}，结果: {review_result["status_name"]}')

        return ResponseUtil.success(data=review_result, msg='审核完成')
    except Exception as e:
        logger.error(f'审核提现申请失败: {str(e)}')
        return ResponseUtil.error(msg=str(e))


@companyWithdrawalController.post(
    '/{withdrawal_id}/process',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:withdrawal:process'))]
)
@Log(title='提现处理', business_type=BusinessType.UPDATE)
async def process_withdrawal_application(
    request: Request,
    withdrawal_id: int = Path(..., description="提现申请ID"),
    process_data: CompanyWithdrawalProcessModel = None,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    处理提现申请
    
    :param request: 请求对象
    :param withdrawal_id: 提现申请ID
    :param process_data: 处理数据
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 处理结果
    """
    try:
        process_result = await CompanyWithdrawalService.process_withdrawal_application_services(
            query_db, withdrawal_id, process_data, current_user
        )

        # 根据处理动作记录不同的日志和返回不同的消息
        if process_data.action == 'complete':
            logger.info(f'处理提现申请成功，申请ID: {withdrawal_id}，交易号: {process_result.get("transaction_no", "N/A")}')
            return ResponseUtil.success(data=process_result, msg='提现处理完成')
        else:  # reject
            logger.info(f'驳回提现申请成功，申请ID: {withdrawal_id}，驳回原因: {process_data.remark}')
            return ResponseUtil.success(data=process_result, msg='提现申请已驳回')
    except Exception as e:
        logger.error(f'处理提现申请失败: {str(e)}')
        return ResponseUtil.error(msg=str(e))


@companyWithdrawalController.post(
    '/{withdrawal_id}/cancel',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:withdrawal:cancel'))]
)
@Log(title='取消提现', business_type=BusinessType.UPDATE)
async def cancel_withdrawal_application(
    request: Request,
    withdrawal_id: int = Path(..., description="提现申请ID"),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    取消提现申请
    
    :param request: 请求对象
    :param withdrawal_id: 提现申请ID
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 取消结果
    """
    try:
        from module_admin.dao.company_withdrawal_dao import CompanyWithdrawalDao
        
        # 获取提现申请
        withdrawal = await CompanyWithdrawalDao.get_company_withdrawal_detail_by_id(query_db, withdrawal_id)
        if not withdrawal:
            return ResponseUtil.error(msg='提现申请不存在')

        # 检查状态是否可以取消
        if withdrawal.status not in ['PENDING', 'APPROVED']:
            return ResponseUtil.error(msg=f'当前状态({CompanyWithdrawalService._get_status_name(withdrawal.status)})不允许取消')

        # 解冻申请金额，返回到余额
        from module_admin.dao.company_transaction_dao import CompanyTransactionDao
        from module_admin.dao.company_dao import CompanyDao

        await CompanyDao.unfreeze_company_funds(
            query_db, withdrawal.company_uuid, withdrawal.apply_amount, return_to_balance=True
        )

        # 查找并更新系统创建的WITHDRAWAL和TAX_DEDUCTION流水状态为已取消
        withdrawal_transaction_records = await CompanyTransactionDao.get_company_transactions_by_related_order_no(
            query_db, withdrawal.withdrawal_no
        )

        for transaction_record in withdrawal_transaction_records:
            if transaction_record.business_type in ['WITHDRAWAL', 'TAX_DEDUCTION']:
                await CompanyTransactionDao.update_transaction_status(
                    query_db, transaction_record.id, 'CANCELLED'
                )

        # 清空原始收入流水的withdrawal_no绑定，方便客户端重新申请提现
        try:
            bound_transaction_records = await CompanyTransactionDao.get_transactions_by_withdrawal_no(
                query_db, withdrawal.withdrawal_no
            )

            for transaction_record in bound_transaction_records:
                await CompanyTransactionDao.update_transaction_withdrawal_no(
                    query_db, transaction_record.id, None
                )
        except Exception as e:
            # 如果数据库字段还没有添加，忽略这个错误
            print(f"Warning: withdrawal_no field may not exist in database: {e}")

        # 更新状态为已取消
        update_data = {
            'updated_by': current_user.user.user_name if current_user else 'system'
        }
        
        success = await CompanyWithdrawalDao.update_withdrawal_status(
            query_db, withdrawal_id, 'CANCELLED', update_data
        )

        if not success:
            return ResponseUtil.error(msg='取消失败')

        await query_db.commit()
        logger.info(f'取消提现申请成功，申请ID: {withdrawal_id}')

        return ResponseUtil.success(msg='提现申请已取消')
    except Exception as e:
        await query_db.rollback()
        logger.error(f'取消提现申请失败: {str(e)}')
        return ResponseUtil.error(msg=str(e))
