from datetime import datetime
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.training_categories_service import TrainingCategoriesService
from module_admin.entity.vo.training_categories_vo import (
    TrainingCategoriesModel, 
    TrainingCategoriesPageQueryModel, 
    DeleteTrainingCategoriesModel
)
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel
from utils.log_util import logger
from module_admin.service.login_service import LoginService
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.annotation.log_annotation import Log
from config.enums import BusinessType


trainingCategoriesController = APIRouter(prefix='/training/categories', dependencies=[Depends(LoginService.get_current_user)])


@trainingCategoriesController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('training:categories:list'))]
)
async def get_training_categories_list(
    request: Request,
    categories_page_query: TrainingCategoriesPageQueryModel = Depends(TrainingCategoriesPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取培训分类列表
    """
    # 获取分页数据
    categories_page_query_result = await TrainingCategoriesService.get_training_categories_list_services(query_db, categories_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=categories_page_query_result)


@trainingCategoriesController.get(
    '/tree', dependencies=[Depends(CheckUserInterfaceAuth('training:categories:list'))]
)
async def get_training_categories_tree(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取培训分类树形结构
    """
    categories_tree_result = await TrainingCategoriesService.get_training_categories_tree_services(query_db)
    logger.info('获取成功')

    return ResponseUtil.success(data=categories_tree_result)


@trainingCategoriesController.get(
    '/{category_id}', dependencies=[Depends(CheckUserInterfaceAuth('training:categories:query'))]
)
async def query_training_categories_detail(
    request: Request,
    category_id: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取培训分类详细信息
    """
    categories_detail_result = await TrainingCategoriesService.training_categories_detail_services(query_db, category_id)
    logger.info('获取成功')

    return ResponseUtil.success(data=categories_detail_result)


@trainingCategoriesController.post(
    '', dependencies=[Depends(CheckUserInterfaceAuth('training:categories:add'))]
)
@Log(title='培训分类管理', business_type=BusinessType.INSERT)
async def add_training_categories(
    request: Request,
    add_categories: TrainingCategoriesModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    新增培训分类
    """
    add_categories.create_time = datetime.now()
    add_categories.update_time = datetime.now()
    add_categories_result = await TrainingCategoriesService.add_training_categories_services(query_db, add_categories)
    logger.info(add_categories_result.message)

    return ResponseUtil.success(msg=add_categories_result.message)


@trainingCategoriesController.put(
    '', dependencies=[Depends(CheckUserInterfaceAuth('training:categories:edit'))]
)
@Log(title='培训分类管理', business_type=BusinessType.UPDATE)
async def edit_training_categories(
    request: Request,
    edit_categories: TrainingCategoriesModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    编辑培训分类
    """
    edit_categories.update_time = datetime.now()
    edit_categories_result = await TrainingCategoriesService.edit_training_categories_services(query_db, edit_categories)
    logger.info(edit_categories_result.message)

    return ResponseUtil.success(msg=edit_categories_result.message)


@trainingCategoriesController.delete(
    '/{category_ids}', dependencies=[Depends(CheckUserInterfaceAuth('training:categories:remove'))]
)
@Log(title='培训分类管理', business_type=BusinessType.DELETE)
async def delete_training_categories(
    request: Request,
    category_ids: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除培训分类
    """
    delete_categories = DeleteTrainingCategoriesModel(ids=category_ids)
    delete_categories_result = await TrainingCategoriesService.delete_training_categories_services(query_db, delete_categories)
    logger.info(delete_categories_result.message)

    return ResponseUtil.success(msg=delete_categories_result.message)
