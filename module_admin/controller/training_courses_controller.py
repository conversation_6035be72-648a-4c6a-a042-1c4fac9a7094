from datetime import datetime
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_admin.service.training_courses_service import TrainingCoursesService
from module_admin.entity.vo.training_courses_vo import (
    TrainingCoursesModel, 
    TrainingCoursesPageQueryModel, 
    DeleteTrainingCoursesModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.response_util import ResponseUtil
from utils.page_util import PageResponseModel
from utils.log_util import logger
from module_admin.service.login_service import LoginService
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.annotation.log_annotation import Log
from config.enums import BusinessType


trainingCoursesController = APIRouter(prefix='/training/courses', dependencies=[Depends(LoginService.get_current_user)])


@trainingCoursesController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('training:courses:list'))]
)
async def get_training_courses_list(
    request: Request,
    courses_page_query: TrainingCoursesPageQueryModel = Depends(TrainingCoursesPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取培训课程列表
    """
    # 获取分页数据
    courses_page_query_result = await TrainingCoursesService.get_training_courses_list_services(query_db, courses_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=courses_page_query_result)


@trainingCoursesController.get(
    '/{course_id}', dependencies=[Depends(CheckUserInterfaceAuth('training:courses:query'))]
)
async def query_training_courses_detail(
    request: Request,
    course_id: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取培训课程详细信息
    """
    courses_detail_result = await TrainingCoursesService.training_courses_detail_services(query_db, course_id)
    logger.info('获取成功')

    return ResponseUtil.success(data=courses_detail_result)


@trainingCoursesController.post(
    '', dependencies=[Depends(CheckUserInterfaceAuth('training:courses:add'))]
)
@Log(title='培训课程管理', business_type=BusinessType.INSERT)
async def add_training_courses(
    request: Request,
    add_courses: TrainingCoursesModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增培训课程
    """
    add_courses.creator_id = str(current_user.user.user_id)
    add_courses.creator_name = current_user.user.user_name
    add_courses.create_time = datetime.now()
    add_courses.update_time = datetime.now()
    add_courses_result = await TrainingCoursesService.add_training_courses_services(query_db, add_courses)
    logger.info(add_courses_result.message)

    return ResponseUtil.success(msg=add_courses_result.message)


@trainingCoursesController.put(
    '', dependencies=[Depends(CheckUserInterfaceAuth('training:courses:edit'))]
)
@Log(title='培训课程管理', business_type=BusinessType.UPDATE)
async def edit_training_courses(
    request: Request,
    edit_courses: TrainingCoursesModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    编辑培训课程
    """
    edit_courses.update_time = datetime.now()
    edit_courses_result = await TrainingCoursesService.edit_training_courses_services(query_db, edit_courses)
    logger.info(edit_courses_result.message)

    return ResponseUtil.success(msg=edit_courses_result.message)


@trainingCoursesController.delete(
    '/{course_ids}', dependencies=[Depends(CheckUserInterfaceAuth('training:courses:remove'))]
)
@Log(title='培训课程管理', business_type=BusinessType.DELETE)
async def delete_training_courses(
    request: Request,
    course_ids: str,
    query_db: AsyncSession = Depends(get_db),
):
    """
    删除培训课程
    """
    delete_courses = DeleteTrainingCoursesModel(ids=course_ids)
    delete_courses_result = await TrainingCoursesService.delete_training_courses_services(query_db, delete_courses)
    logger.info(delete_courses_result.message)

    return ResponseUtil.success(msg=delete_courses_result.message)
