from datetime import datetime
from fastapi import API<PERSON>outer, Depends, Request
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService, oauth2_scheme
from module_admin.service.experience_table_service import ExperienceTableService
from module_admin.service.follow_up_record_service import FollowUpRecordService
from module_admin.entity.vo.experience_table_vo import DeleteExperienceTableModel, ExperienceTableModel, ExperienceTablePageQueryModel, ExperienceTableQueryModel
from module_admin.entity.vo.follow_up_record_vo import FollowUpRecordModel, FollowUpRecordPageQueryModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


experienceTableController = APIRouter(prefix='/merchant/experienceTable', dependencies=[Depends(LoginService.get_current_user)])


@experienceTableController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:list'))]
)
async def get_merchant_experience_table_list(
    request: Request,
    experience_table_page_query: ExperienceTablePageQueryModel = Depends(ExperienceTablePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取分页数据
    experience_table_page_query_result = await ExperienceTableService.get_experience_table_list_services(query_db, experience_table_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=experience_table_page_query_result)


@experienceTableController.post('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:add'))])
@Log(title='商户管理', business_type=BusinessType.INSERT)
async def add_merchant_experience_table(
    request: Request,
    add_experience_table: ExperienceTableModel,
    query_db: AsyncSession = Depends(get_db),
):
    add_experience_table_result = await ExperienceTableService.add_experience_table_services(query_db, add_experience_table)
    logger.info(add_experience_table_result.message)

    return ResponseUtil.success(msg=add_experience_table_result.message)


@experienceTableController.put('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:edit'))])
@Log(title='商户管理', business_type=BusinessType.UPDATE)
async def edit_merchant_experience_table(
    request: Request,
    edit_experience_table: ExperienceTableModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    编辑商户信息并创建公司、门店、门店信息和软件版本关联

    :param request: 请求对象
    :param edit_experience_table: 编辑商户对象
    :param query_db: 数据库会话
    :param current_user: 当前登录用户
    :return: 编辑结果
    """
    try:
        # 先更新商户信息
        edit_experience_table_result = await ExperienceTableService.edit_experience_table_services(query_db, edit_experience_table)

        # 如果商户信息更新成功，创建公司、门店和版本关联
        if edit_experience_table_result.is_success:
            # 导入公司服务
            from module_admin.service.company_service import CompanyService

            # 获取完整的商户信息
            experience_table = await ExperienceTableService.experience_table_detail_services(query_db, edit_experience_table.id)

            # 创建公司、门店和版本关联
            create_result = await CompanyService.create_company_with_store_service(
                query_db,
                experience_table,
                current_user
            )

            logger.info(f'编辑商户信息并创建公司、门店和版本关联成功')
            return ResponseUtil.success(msg=f"编辑商户信息成功，并{create_result.message}")

        logger.info(edit_experience_table_result.message)
        return ResponseUtil.success(msg=edit_experience_table_result.message)
    except Exception as e:
        logger.error(f"编辑商户信息或创建公司、门店和版本关联失败: {str(e)}")
        return ResponseUtil.error(msg=f"操作失败: {str(e)}")


@experienceTableController.delete('/{ids}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:remove'))])
@Log(title='商户管理', business_type=BusinessType.DELETE)
async def delete_merchant_experience_table(request: Request, ids: str, query_db: AsyncSession = Depends(get_db)):
    delete_experience_table = DeleteExperienceTableModel(ids=ids)
    delete_experience_table_result = await ExperienceTableService.delete_experience_table_services(query_db, delete_experience_table)
    logger.info(delete_experience_table_result.message)

    return ResponseUtil.success(msg=delete_experience_table_result.message)


@experienceTableController.get(
    '/followUpRecords/{merchant_id}', response_model=PageResponseModel,
    dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:query'))]
)
async def get_merchant_follow_up_records(
    request: Request,
    merchant_id: int,
    follow_up_record_page_query: FollowUpRecordPageQueryModel = Depends(FollowUpRecordPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取商户跟进记录列表

    :param request: 请求对象
    :param merchant_id: 商户ID
    :param follow_up_record_page_query: 查询参数
    :param query_db: 数据库会话
    :return: 跟进记录列表
    """
    follow_up_records_result = await FollowUpRecordService.get_follow_up_records_by_merchant_id_services(
        query_db, merchant_id, follow_up_record_page_query, is_page=True
    )
    logger.info(f'获取商户ID为{merchant_id}的跟进记录成功')

    return ResponseUtil.success(model_content=follow_up_records_result)


@experienceTableController.get(
    '/{id}', response_model=ExperienceTableModel, dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:query'))]
)
async def query_detail_merchant_experience_table(request: Request, id: int, query_db: AsyncSession = Depends(get_db)):
    experience_table_detail_result = await ExperienceTableService.experience_table_detail_services(query_db, id)
    logger.info(f'获取id为{id}的信息成功')

    return ResponseUtil.success(data=experience_table_detail_result)


@experienceTableController.post(
    '/examine/{id}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:edit'))]
)
@Log(title='商户管理', business_type=BusinessType.OTHER)
async def examine_merchant_experience_table(
    request: Request,
    id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    审核商户并创建公司、门店、门店信息和软件版本关联

    :param request: 请求对象
    :param id: 商户ID
    :param query_db: 数据库会话
    :param current_user: 当前登录用户
    :return: 审核结果
    """
    try:
        # 获取商户信息
        experience_table = await ExperienceTableService.experience_table_detail_services(query_db, id)
        if not experience_table or not experience_table.company_name:
            return ResponseUtil.error(msg=f"未找到ID为{id}的商户信息或公司名称为空")

        # 导入公司服务
        from module_admin.service.company_service import CompanyService

        # 开始事务
        try:
            # 创建公司、门店和版本关联
            create_result = await CompanyService.create_company_with_store_service(
                query_db,
                experience_table,
                current_user
            )

            # 获取创建的公司和门店信息
            company_dict = create_result.result.get('company')
            store_dict = create_result.result.get('store')

            # 为新创建的公司添加支付类型
            if company_dict:
                pay_types_result = await CompanyService.create_pay_types_service(
                    query_db,
                    company_dict['id'],
                    current_user
                )
                logger.info(f'为公司{company_dict["id"]}创建支付类型成功：{pay_types_result.message}')

                # 为新创建的公司添加初始产品
                products_result = await CompanyService.create_initial_products_service(
                    query_db,
                    company_dict['id'],
                    current_user
                )
                logger.info(f'为公司{company_dict["id"]}创建初始产品成功：{products_result.message}')

            # 更新商户状态为通过(1)
            update_status_result = await ExperienceTableService.update_experience_table_status_services(query_db, id, 1)

            # 创建内部用户账号
            if company_dict and store_dict:
                user_result = await CompanyService.create_internal_user_service(
                    query_db,
                    company_dict['id'],
                    store_dict['store_uuid'],
                    store_dict['id'],
                    company_dict['name'],
                    store_dict['name'],
                    experience_table,  # 传入experience_table对象
                    current_user
                )

                # 提交事务
                await query_db.commit()

                # 发送审核通过短信
                try:
                    from utils.sms_util import SmsUtil
                    user_info = user_result.result
                    sms_result = await SmsUtil.send_audit_success_sms(
                        user_info['mobile'],
                        user_info['mobile'],  # userno参数使用手机号而不是用户名
                        user_info['password']
                    )
                    if sms_result['success']:
                        logger.info(f'审核通过短信发送成功：手机号={user_info["mobile"]}')
                    else:
                        logger.error(f'审核通过短信发送失败：{sms_result["message"]}')
                except Exception as e:
                    logger.error(f'发送审核通过短信异常：{str(e)}')

                logger.info(f'审核商户成功：创建公司、门店和版本关联成功，创建支付类型成功，创建初始产品成功，创建内部用户账号成功，并更新id为{id}的商户状态为通过(1)')
                return ResponseUtil.success(msg=f"审核通过：{create_result.message}，{pay_types_result.message if 'pay_types_result' in locals() else ''}，{products_result.message if 'products_result' in locals() else ''}，{update_status_result.message}，{user_result.message}")

            # 提交事务
            await query_db.commit()

            logger.info(f'审核商户成功：创建公司、门店和版本关联成功，创建支付类型成功，创建初始产品成功，并更新id为{id}的商户状态为通过(1)')
            return ResponseUtil.success(msg=f"审核通过：{create_result.message}，{pay_types_result.message if 'pay_types_result' in locals() else ''}，{products_result.message if 'products_result' in locals() else ''}，{update_status_result.message}")

        except Exception as e:
            # 回滚事务
            await query_db.rollback()
            raise e

    except Exception as e:
        logger.error(f"审核商户失败: {str(e)}")
        return ResponseUtil.error(msg=f"审核失败: {str(e)}")


@experienceTableController.post(
    '/examineEmployee/{id}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:edit'))]
)
@Log(title='商户管理', business_type=BusinessType.OTHER)
async def examine_employee_experience_table(
    request: Request,
    id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    员工审核商户并创建门店、门店信息和内部用户账号（绑定到统一公司）

    :param request: 请求对象
    :param id: 商户ID
    :param query_db: 数据库会话
    :param current_user: 当前登录用户
    :return: 审核结果
    """
    try:
        # 获取商户信息
        experience_table = await ExperienceTableService.experience_table_detail_services(query_db, id)
        if not experience_table or not experience_table.company_name:
            return ResponseUtil.error(msg=f"未找到ID为{id}的商户信息或公司名称为空")

        # 导入公司服务
        from module_admin.service.company_service import CompanyService

        # 开始事务
        try:
            # 创建员工门店（绑定到统一公司）
            create_result = await CompanyService.create_employee_store_service(
                query_db,
                experience_table,
                current_user
            )

            # 获取创建的公司和门店信息
            company_dict = create_result.result.get('company')
            store_dict = create_result.result.get('store')

            # 更新商户状态为通过(1)
            update_status_result = await ExperienceTableService.update_experience_table_status_services(query_db, id, 1)

            # 创建内部用户账号
            if company_dict and store_dict:
                user_result = await CompanyService.create_internal_user_service(
                    query_db,
                    company_dict['id'],
                    store_dict['store_uuid'],
                    store_dict['id'],
                    company_dict['name'],
                    store_dict['name'],
                    experience_table,  # 传入experience_table对象
                    current_user
                )

                # 提交事务
                await query_db.commit()

                # 发送审核通过短信
                try:
                    from utils.sms_util import SmsUtil
                    user_info = user_result.result
                    sms_result = await SmsUtil.send_audit_success_sms(
                        user_info['mobile'],
                        user_info['mobile'],  # userno参数使用手机号而不是用户名
                        user_info['password']
                    )
                    if sms_result['success']:
                        logger.info(f'员工审核通过短信发送成功：手机号={user_info["mobile"]}')
                    else:
                        logger.error(f'员工审核通过短信发送失败：{sms_result["message"]}')
                except Exception as e:
                    logger.error(f'发送员工审核通过短信异常：{str(e)}')

                logger.info(f'员工审核商户成功：创建员工门店成功，创建内部用户账号成功，并更新id为{id}的商户状态为通过(1)')
                return ResponseUtil.success(msg=f"员工审核通过：{create_result.message}，{update_status_result.message}，{user_result.message}")

            # 提交事务
            await query_db.commit()

            logger.info(f'员工审核商户成功：创建员工门店成功，并更新id为{id}的商户状态为通过(1)')
            return ResponseUtil.success(msg=f"员工审核通过：{create_result.message}，{update_status_result.message}")

        except Exception as e:
            # 回滚事务
            await query_db.rollback()
            raise e

    except Exception as e:
        logger.error(f"员工审核商户失败: {str(e)}")
        return ResponseUtil.error(msg=f"员工审核失败: {str(e)}")


@experienceTableController.post(
    '/followUpRecord', dependencies=[Depends(CheckUserInterfaceAuth('merchant:experienceTable:add'))]
)
@Log(title='商户管理', business_type=BusinessType.INSERT)
async def add_merchant_follow_up_record(
    request: Request,
    follow_up_record: FollowUpRecordModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增商户跟进记录

    :param request: 请求对象
    :param follow_up_record: 跟进记录对象
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    # 设置创建者
    follow_up_record.create_by = current_user.user.user_name

    # 如果没有设置跟进人，则使用当前用户名
    if not follow_up_record.follow_up_person:
        follow_up_record.follow_up_person = current_user.user.user_name

    # 如果没有设置跟进时间，则使用当前时间
    if not follow_up_record.follow_up_time:
        follow_up_record.follow_up_time = datetime.now()

    add_follow_up_record_result = await FollowUpRecordService.add_follow_up_record_services(query_db, follow_up_record)
    logger.info(add_follow_up_record_result.message)

    return ResponseUtil.success(msg=add_follow_up_record_result.message)