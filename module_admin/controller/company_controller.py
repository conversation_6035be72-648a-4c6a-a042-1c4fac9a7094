from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.company_vo import CompanyModel, CompanyPageQueryModel, DeleteCompanyModel, CompanyRechargeModel, BatchRenewCompanyVersionsModel, UndoBatchRenewModel, CompanyFeeRateModel
from module_admin.service.company_service import CompanyService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from typing import List
from typing import List


companyController = APIRouter(prefix='/merchant/company', dependencies=[Depends(LoginService.get_current_user)])


@companyController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:list'))]
)
async def get_merchant_company_list(
    request: Request,
    company_page_query: CompanyPageQueryModel = Depends(CompanyPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取公司列表
    """
    # 获取分页数据
    company_page_query_result = await CompanyService.get_company_list_services(query_db, company_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=company_page_query_result)


# 注意：具体路由必须在参数化路由之前定义，避免路由冲突

@companyController.get('/available-versions', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:query'))])
async def get_available_versions(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取可用的软件版本列表
    """
    versions_result = await CompanyService.get_available_versions_services(query_db)
    logger.info('获取可用版本列表成功')

    return ResponseUtil.success(data=versions_result)


@companyController.get('/{company_id}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:query'))])
async def query_merchant_company_detail(
    request: Request, company_id: str, query_db: AsyncSession = Depends(get_db)
):
    """
    获取公司详细信息
    """
    company_detail_result = await CompanyService.company_detail_services(query_db, company_id)
    logger.info(f'获取company_id为{company_id}的信息成功')

    return ResponseUtil.success(data=company_detail_result)


@companyController.post('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:add'))])
@Log(title='公司管理', business_type=BusinessType.INSERT)
async def add_merchant_company(
    request: Request,
    add_company: CompanyModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    新增公司
    """
    add_company_result = await CompanyService.add_company_services(query_db, add_company)
    logger.info(add_company_result.message)

    return ResponseUtil.success(msg=add_company_result.message)


@companyController.put('', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:edit'))])
@Log(title='公司管理', business_type=BusinessType.UPDATE)
async def edit_merchant_company(
    request: Request,
    edit_company: CompanyModel,
    query_db: AsyncSession = Depends(get_db),
):
    """
    修改公司
    """
    edit_company_result = await CompanyService.edit_company_services(query_db, edit_company)
    logger.info(edit_company_result.message)

    return ResponseUtil.success(msg=edit_company_result.message)


@companyController.get('/{company_id}/stores', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:query'))])
async def get_company_stores_detail(
    request: Request, company_id: str, query_db: AsyncSession = Depends(get_db)
):
    """
    获取公司门店详情信息
    """
    stores_detail_result = await CompanyService.get_company_stores_detail_services(query_db, company_id)
    logger.info(f'获取company_id为{company_id}的门店详情信息成功')

    return ResponseUtil.success(data=stores_detail_result)


@companyController.delete('/{ids}', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:remove'))])
@Log(title='公司管理', business_type=BusinessType.DELETE)
async def delete_merchant_company(request: Request, ids: str, query_db: AsyncSession = Depends(get_db)):
    """
    删除公司
    """
    delete_company = DeleteCompanyModel(ids=ids)
    delete_company_result = await CompanyService.delete_company_services(query_db, delete_company)
    logger.info(delete_company_result.message)

    return ResponseUtil.success(msg=delete_company_result.message)


@companyController.post('/{company_id}/recharge', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:recharge'))])
@Log(title='公司管理', business_type=BusinessType.OTHER)
async def company_recharge(
    request: Request,
    company_id: str,
    recharge_request: CompanyRechargeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    公司充值
    """
    # 设置公司ID
    recharge_request.company_id = company_id

    # 调用充值服务
    recharge_result = await CompanyService.company_recharge_service(query_db, recharge_request, current_user)
    logger.info(f'公司{company_id}充值成功，金额：{recharge_request.amount}')

    return ResponseUtil.success(msg=recharge_result.message, data=recharge_result.result)


@companyController.get('/{company_id}/versions', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:query'))])
async def get_company_versions(
    request: Request,
    company_id: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取公司关联的版本信息
    """
    company_versions_result = await CompanyService.get_company_versions_services(query_db, company_id)
    logger.info(f'获取公司{company_id}的版本信息成功')

    return ResponseUtil.success(data=company_versions_result)





@companyController.post('/{company_id}/versions', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:edit'))])
@Log(title='公司版本管理', business_type=BusinessType.INSERT)
async def add_company_version(
    request: Request,
    company_id: str,
    version_data: dict,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    为公司新增版本关联
    """
    add_version_result = await CompanyService.add_company_version_services(query_db, company_id, version_data, current_user)
    logger.info(f'为公司{company_id}新增版本关联成功')

    return ResponseUtil.success(msg=add_version_result.message)


@companyController.get('/{company_id}/versions/detail', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:query'))])
async def get_company_versions_detail(
    request: Request,
    company_id: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取公司版本详细信息（包含到期时间）
    """
    versions_detail_result = await CompanyService.get_company_versions_detail_services(query_db, company_id)
    logger.info(f'获取公司{company_id}的版本详细信息成功')

    return ResponseUtil.success(data=versions_detail_result)


@companyController.put('/{company_id}/versions/renew', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:edit'))])
@Log(title='公司版本续费', business_type=BusinessType.UPDATE)
async def renew_company_versions(
    request: Request,
    company_id: str,
    renew_data: List[dict],
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    批量更新公司版本到期时间（续费）
    """
    renew_result = await CompanyService.renew_company_versions_services(query_db, company_id, renew_data, current_user)
    logger.info(f'公司{company_id}版本续费成功')

    return ResponseUtil.success(msg=renew_result.message)


@companyController.post('/batch/versions/renew', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:edit'))])
@Log(title='批量公司版本续费', business_type=BusinessType.UPDATE)
async def batch_renew_company_versions(
    request: Request,
    batch_renew_data: BatchRenewCompanyVersionsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    批量续费多个公司的版本
    """
    batch_renew_result = await CompanyService.batch_renew_company_versions_services(query_db, batch_renew_data, current_user)
    logger.info(f'批量续费成功，涉及{len(batch_renew_data.company_ids)}个公司')

    return ResponseUtil.success(msg=batch_renew_result.message, data=batch_renew_result.result)


@companyController.post('/batch/versions/renew/undo', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:edit'))])
@Log(title='撤销批量续费', business_type=BusinessType.UPDATE)
async def undo_batch_renew_company_versions(
    request: Request,
    undo_data: UndoBatchRenewModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    撤销批量续费操作
    """
    undo_result = await CompanyService.undo_batch_renew_company_versions_services(query_db, undo_data, current_user)
    logger.info(f'撤销批量续费操作成功，操作ID：{undo_data.operation_id}')

    return ResponseUtil.success(msg=undo_result.message)


@companyController.get('/{company_id}/fee-rate', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:query'))])
async def get_company_fee_rate(
    request: Request,
    company_id: str,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取公司费率信息
    """
    fee_rate_result = await CompanyService.get_company_fee_rate_services(query_db, company_id)
    logger.info(f'获取公司{company_id}费率信息成功')

    return ResponseUtil.success(msg=fee_rate_result.message, data=fee_rate_result.result)


@companyController.put('/{company_id}/fee-rate', dependencies=[Depends(CheckUserInterfaceAuth('merchant:company:edit'))])
@Log(title='公司费率管理', business_type=BusinessType.UPDATE)
async def update_company_fee_rate(
    request: Request,
    company_id: str,
    fee_rate_data: CompanyFeeRateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    更新公司费率信息
    """
    # 设置公司ID
    fee_rate_data.company_id = company_id

    fee_rate_result = await CompanyService.update_company_fee_rate_services(query_db, fee_rate_data, current_user)
    logger.info(f'更新公司{company_id}费率信息成功')

    return ResponseUtil.success(msg=fee_rate_result.message, data=fee_rate_result.result)
