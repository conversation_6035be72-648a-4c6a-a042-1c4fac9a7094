from fastapi import APIRouter, Depends, Request, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.company_transaction_vo import (
    CompanyTransactionModel,
    CompanyTransactionPageQueryModel,
    CompanyTransactionResponseModel
)
from module_admin.service.company_transaction_service import CompanyTransactionService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


companyTransactionController = APIRouter(
    prefix='/finance/transaction',
    dependencies=[Depends(LoginService.get_current_user)]
)


@companyTransactionController.get(
    '/list', 
    response_model=PageResponseModel, 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:list'))]
)
async def get_company_transaction_list(
    request: Request,
    transaction_page_query: CompanyTransactionPageQueryModel = Depends(CompanyTransactionPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取公司资金流水列表
    
    :param request: 请求对象
    :param transaction_page_query: 分页查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 公司资金流水列表
    """
    # 获取分页数据
    transaction_page_query_result = await CompanyTransactionService.get_company_transaction_list_services(
        query_db, transaction_page_query, is_page=True
    )
    logger.info('获取公司资金流水列表成功')

    return ResponseUtil.success(model_content=transaction_page_query_result)


@companyTransactionController.post(
    '/export',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:export'))]
)
@Log(title='公司资金流水', business_type=BusinessType.EXPORT)
async def export_company_transaction_list(
    request: Request,
    transaction_query: CompanyTransactionPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    导出公司资金流水列表

    :param request: 请求对象
    :param transaction_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 导出数据
    """
    # 检查是否有前端处理好的导出数据
    if transaction_query.export_data:
        import json
        # 使用前端传递的数据
        transaction_list_result = json.loads(transaction_query.export_data)
    else:
        # 获取全部数据（不分页）
        transaction_list_result = await CompanyTransactionService.get_company_transaction_list_services(
            query_db, transaction_query, is_page=False
        )

    # 导出为Excel格式
    transaction_export_result = await CompanyTransactionService.export_company_transaction_list_services(transaction_list_result)
    logger.info('导出公司资金流水列表成功')

    return ResponseUtil.streaming(data=bytes2file_response(transaction_export_result))


@companyTransactionController.get(
    '/{transaction_id}',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:query'))]
)
async def get_company_transaction_detail(
    request: Request,
    transaction_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取公司资金流水详情
    
    :param request: 请求对象
    :param transaction_id: 流水ID
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 公司资金流水详情
    """
    transaction_detail_result = await CompanyTransactionService.get_company_transaction_detail_services(
        query_db, transaction_id
    )
    logger.info(f'获取公司资金流水详情成功，流水ID: {transaction_id}')

    return ResponseUtil.success(data=transaction_detail_result)
