from fastapi import APIRouter, Depends, Request, Form, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.company_transaction_vo import (
    CompanyTransactionModel,
    CompanyTransactionPageQueryModel
)
from module_admin.service.software_income_service import SoftwareIncomeService
from module_admin.service.login_service import LoginService, CurrentUserModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


softwareIncomeController = APIRouter(
    prefix='/finance/software-income',
    dependencies=[Depends(LoginService.get_current_user)]
)


@softwareIncomeController.get(
    '/list', 
    response_model=PageResponseModel, 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:list'))]
)
async def get_software_income_list(
    request: Request,
    transaction_page_query: CompanyTransactionPageQueryModel = Depends(CompanyTransactionPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取软件收入流水列表
    
    :param request: 请求对象
    :param transaction_page_query: 分页查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 软件收入流水列表
    """
    # 强制设置业务类型为软件续费
    transaction_page_query.business_type = 'SOFTWARE_RENEWAL'
    # 强制设置交易类型为支出（相对于平台是收入）
    transaction_page_query.transaction_type = 2
    
    # 获取分页数据
    transaction_page_query_result = await SoftwareIncomeService.get_software_income_list_services(
        query_db, transaction_page_query, is_page=True
    )
    logger.info('获取软件收入流水列表成功')

    return ResponseUtil.success(model_content=transaction_page_query_result)


@softwareIncomeController.post(
    '/export', 
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:export'))]
)
@Log(title='软件收入流水', business_type=BusinessType.EXPORT)
async def export_software_income_list(
    request: Request,
    transaction_page_query: CompanyTransactionPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    导出软件收入流水列表
    
    :param request: 请求对象
    :param transaction_page_query: 查询参数
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件
    """
    # 强制设置业务类型为软件续费
    transaction_page_query.business_type = 'SOFTWARE_RENEWAL'
    # 强制设置交易类型为支出（相对于平台是收入）
    transaction_page_query.transaction_type = 2
    
    # 获取全量数据
    transaction_query_result = await SoftwareIncomeService.get_software_income_list_services(
        query_db, transaction_page_query, is_page=False
    )
    transaction_export_result = await SoftwareIncomeService.export_software_income_list_services(transaction_query_result)
    logger.info('导出软件收入流水列表成功')

    return ResponseUtil.streaming(data=bytes2file_response(transaction_export_result))


@softwareIncomeController.get(
    '/statistics',
    dependencies=[Depends(CheckUserInterfaceAuth('finance:transaction:list'))]
)
async def get_software_income_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取软件收入统计数据（不受筛选条件影响）

    :param request: 请求对象
    :param query_db: 数据库会话
    :param current_user: 当前用户
    :return: 软件收入统计数据
    """
    # 获取统计数据（统计条件在service层设定）
    statistics_result = await SoftwareIncomeService.get_software_income_statistics_services(
        query_db
    )
    logger.info('获取软件收入统计数据成功')

    return ResponseUtil.success(data=statistics_result)


